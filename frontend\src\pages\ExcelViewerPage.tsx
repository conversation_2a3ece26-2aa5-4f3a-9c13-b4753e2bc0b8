import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { FiArrowLeft, FiHome } from 'react-icons/fi';
import ExcelViewer from '../components/ExcelViewer';
import { fileApi } from '../services/api';

const ExcelViewerPage: React.FC = () => {
  const { fileId } = useParams<{ fileId: string }>();
  const navigate = useNavigate();

  // Check if file exists and get basic info
  const { data: metadata, isLoading, error } = useQuery({
    queryKey: ['file-metadata', fileId],
    queryFn: () => fileApi.getFileMetadata(fileId!),
    enabled: !!fileId,
  });

  const handleGoBack = () => {
    navigate('/');
  };

  if (!fileId) {
    return (
      <div className="excel-content">
        <div className="error">
          <div>Invalid file ID</div>
          <button className="btn" onClick={handleGoBack} style={{ marginTop: '1rem' }}>
            <FiHome style={{ marginRight: '0.5rem' }} />
            Go Home
          </button>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="excel-content">
        <div className="loading">
          <div>Loading file information...</div>
        </div>
      </div>
    );
  }

  if (error || !metadata) {
    return (
      <div className="excel-content">
        <div className="error">
          <div>File not found or failed to load</div>
          <button className="btn" onClick={handleGoBack} style={{ marginTop: '1rem' }}>
            <FiArrowLeft style={{ marginRight: '0.5rem' }} />
            Go Back
          </button>
        </div>
      </div>
    );
  }

  if (metadata.status === 'error') {
    return (
      <div className="excel-content">
        <div className="error">
          <div>
            <h3>Processing Error</h3>
            <p>{metadata.error_message || 'Failed to process the Excel file'}</p>
          </div>
          <button className="btn" onClick={handleGoBack} style={{ marginTop: '1rem' }}>
            <FiArrowLeft style={{ marginRight: '0.5rem' }} />
            Go Back
          </button>
        </div>
      </div>
    );
  }

  if (metadata.status === 'processing') {
    return (
      <div className="excel-content">
        <div className="loading">
          <div>
            <h3>Processing Excel File</h3>
            <p>Please wait while we process "{metadata.filename}"...</p>
            <div style={{ marginTop: '1rem' }}>
              <div style={{
                width: '200px',
                height: '4px',
                backgroundColor: '#e9ecef',
                borderRadius: '2px',
                overflow: 'hidden',
                margin: '0 auto'
              }}>
                <div style={{
                  width: '50%',
                  height: '100%',
                  backgroundColor: '#007bff',
                  animation: 'progress 2s ease-in-out infinite'
                }} />
              </div>
            </div>
            <p style={{ fontSize: '0.9rem', color: '#6c757d', marginTop: '1rem' }}>
              This usually takes a few seconds...
            </p>
          </div>
          <button className="btn" onClick={handleGoBack} style={{ marginTop: '2rem' }}>
            <FiArrowLeft style={{ marginRight: '0.5rem' }} />
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="excel-content">
      {/* Breadcrumb */}
      <div style={{
        padding: '0.5rem 1rem',
        backgroundColor: '#f8f9fa',
        borderBottom: '1px solid #dee2e6',
        display: 'flex',
        alignItems: 'center',
        gap: '1rem'
      }}>
        <button
          className="btn"
          onClick={handleGoBack}
          style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}
        >
          <FiArrowLeft size={16} />
          Back to Files
        </button>
        
        <span style={{ color: '#6c757d' }}>›</span>
        
        <span style={{ fontWeight: 'bold' }}>{metadata.filename}</span>
        
        <div style={{ marginLeft: 'auto', fontSize: '0.9rem', color: '#6c757d' }}>
          {metadata.total_sheets} sheet{metadata.total_sheets !== 1 ? 's' : ''}
          {metadata.has_formulas && ' • Contains formulas'}
          {metadata.has_images && ' • Contains images'}
        </div>
      </div>

      {/* Excel Viewer */}
      <ExcelViewer fileId={fileId} />
    </div>
  );
};

export default ExcelViewerPage;
