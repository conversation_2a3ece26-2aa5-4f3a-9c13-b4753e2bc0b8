# Deployment Guide

This guide covers deploying the Excel Web Application in various environments.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Development Deployment](#development-deployment)
3. [Production Deployment](#production-deployment)
4. [Cloud Deployment](#cloud-deployment)
5. [Configuration](#configuration)
6. [Monitoring](#monitoring)
7. [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements

**Minimum Requirements:**
- CPU: 2 cores
- RAM: 4GB
- Storage: 10GB free space
- OS: Linux (Ubuntu 20.04+), macOS, or Windows with WSL2

**Recommended for Production:**
- CPU: 4+ cores
- RAM: 8GB+
- Storage: 50GB+ SSD
- OS: Linux (Ubuntu 22.04 LTS)

### Software Dependencies

- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Git**: For cloning the repository
- **Make**: For using Makefile commands (optional)

### Network Requirements

**Ports:**
- `3000`: Frontend application
- `8000`: Backend API
- `6379`: Redis (internal)

**Firewall Rules:**
- Allow inbound traffic on ports 3000 and 8000
- Allow outbound traffic for package downloads

## Development Deployment

### Quick Start

```bash
# Clone repository
git clone <repository-url>
cd excel-web-app

# Automated setup
./setup.sh

# Or manual setup
make setup
```

### Manual Development Setup

```bash
# 1. Clone and navigate
git clone <repository-url>
cd excel-web-app

# 2. Create environment file
cp .env.example .env

# 3. Create directories
mkdir -p uploads temp logs

# 4. Build and start services
docker-compose up --build

# 5. Verify deployment
curl http://localhost:8000/health
curl http://localhost:3000
```

### Development Configuration

Edit `.env` file for development:

```bash
# Development settings
DEBUG=true
ENVIRONMENT=development
LOG_LEVEL=DEBUG

# Server configuration
HOST=0.0.0.0
PORT=8000
WORKERS=1

# File storage
UPLOAD_DIR=./uploads
TEMP_DIR=./temp
MAX_FILE_SIZE=100MB

# Performance (development)
MAX_CONCURRENT_CONVERSIONS=3
CACHE_TTL=1800
```

## Production Deployment

### Production Setup

```bash
# 1. Clone repository
git clone <repository-url>
cd excel-web-app

# 2. Create production environment
cp .env.example .env
# Edit .env with production settings (see below)

# 3. Start production services
docker-compose -f docker-compose.prod.yml up --build -d

# 4. Verify deployment
make health
```

### Production Configuration

Edit `.env` file for production:

```bash
# Production settings
DEBUG=false
ENVIRONMENT=production
LOG_LEVEL=INFO

# Server configuration
HOST=0.0.0.0
PORT=8000
WORKERS=4

# Security
SECRET_KEY=your-very-secure-secret-key-here
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Database (if using PostgreSQL)
DATABASE_URL=postgresql://user:password@localhost:5432/excel_app

# Redis
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your-secure-redis-password

# File storage
UPLOAD_DIR=/app/uploads
TEMP_DIR=/app/temp
MAX_FILE_SIZE=100MB

# Performance (production)
MAX_CONCURRENT_CONVERSIONS=10
CACHE_TTL=3600
CLEANUP_INTERVAL=1800

# Monitoring
LOG_FORMAT=json
```

### SSL/TLS Configuration

For HTTPS in production, configure SSL certificates:

```bash
# Create SSL directory
mkdir -p ssl

# Copy your SSL certificates
cp your-domain.crt ssl/
cp your-domain.key ssl/

# Update nginx configuration
# Edit nginx.prod.conf to include SSL settings
```

Example SSL nginx configuration:

```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    ssl_certificate /etc/nginx/ssl/your-domain.crt;
    ssl_certificate_key /etc/nginx/ssl/your-domain.key;
    
    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # Your existing configuration...
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}
```

### Database Configuration

#### SQLite (Default)
```bash
DATABASE_URL=sqlite:///./excel_app.db
```

#### PostgreSQL (Recommended for Production)
```bash
# Install PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# Create database and user
sudo -u postgres psql
CREATE DATABASE excel_app;
CREATE USER excel_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE excel_app TO excel_user;
\q

# Update environment
DATABASE_URL=postgresql://excel_user:secure_password@localhost:5432/excel_app
```

## Cloud Deployment

### AWS Deployment

#### Using EC2

```bash
# 1. Launch EC2 instance (t3.medium or larger)
# 2. Install Docker
sudo yum update -y
sudo yum install -y docker
sudo service docker start
sudo usermod -a -G docker ec2-user

# 3. Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 4. Clone and deploy
git clone <repository-url>
cd excel-web-app
cp .env.example .env
# Edit .env with production settings
docker-compose -f docker-compose.prod.yml up -d
```

#### Using ECS (Elastic Container Service)

1. Create ECS cluster
2. Build and push images to ECR
3. Create task definitions
4. Deploy services

```bash
# Build and push to ECR
aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin <account-id>.dkr.ecr.us-west-2.amazonaws.com

docker build -t excel-app-backend ./backend
docker tag excel-app-backend:latest <account-id>.dkr.ecr.us-west-2.amazonaws.com/excel-app-backend:latest
docker push <account-id>.dkr.ecr.us-west-2.amazonaws.com/excel-app-backend:latest

docker build -t excel-app-frontend ./frontend
docker tag excel-app-frontend:latest <account-id>.dkr.ecr.us-west-2.amazonaws.com/excel-app-frontend:latest
docker push <account-id>.dkr.ecr.us-west-2.amazonaws.com/excel-app-frontend:latest
```

### Google Cloud Platform

#### Using Compute Engine

```bash
# 1. Create VM instance
gcloud compute instances create excel-app-vm \
    --image-family=ubuntu-2004-lts \
    --image-project=ubuntu-os-cloud \
    --machine-type=e2-standard-2 \
    --zone=us-central1-a

# 2. SSH and setup
gcloud compute ssh excel-app-vm --zone=us-central1-a

# 3. Install Docker and deploy (same as EC2 steps)
```

#### Using Cloud Run

```bash
# Build and deploy to Cloud Run
gcloud builds submit --tag gcr.io/PROJECT-ID/excel-app-backend ./backend
gcloud run deploy excel-app-backend --image gcr.io/PROJECT-ID/excel-app-backend --platform managed

gcloud builds submit --tag gcr.io/PROJECT-ID/excel-app-frontend ./frontend
gcloud run deploy excel-app-frontend --image gcr.io/PROJECT-ID/excel-app-frontend --platform managed
```

### Azure Deployment

#### Using Container Instances

```bash
# Create resource group
az group create --name excel-app-rg --location eastus

# Deploy containers
az container create \
    --resource-group excel-app-rg \
    --name excel-app-backend \
    --image your-registry/excel-app-backend:latest \
    --ports 8000 \
    --environment-variables DEBUG=false ENVIRONMENT=production

az container create \
    --resource-group excel-app-rg \
    --name excel-app-frontend \
    --image your-registry/excel-app-frontend:latest \
    --ports 3000
```

### DigitalOcean Deployment

#### Using Droplets

```bash
# 1. Create droplet (4GB RAM minimum)
# 2. SSH and install Docker
sudo apt update
sudo apt install -y docker.io docker-compose
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER

# 3. Deploy application
git clone <repository-url>
cd excel-web-app
cp .env.example .env
# Edit .env with production settings
docker-compose -f docker-compose.prod.yml up -d
```

## Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `DEBUG` | Enable debug mode | `false` | No |
| `ENVIRONMENT` | Environment name | `production` | No |
| `SECRET_KEY` | Application secret key | - | Yes |
| `DATABASE_URL` | Database connection URL | `sqlite:///./excel_app.db` | No |
| `REDIS_HOST` | Redis host | `redis` | No |
| `REDIS_PASSWORD` | Redis password | - | No |
| `MAX_FILE_SIZE` | Maximum upload size | `100MB` | No |
| `CORS_ORIGINS` | Allowed CORS origins | `*` | No |

### Resource Limits

Configure resource limits in `docker-compose.prod.yml`:

```yaml
services:
  backend:
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '0.5'
```

### Backup Configuration

```bash
# Create backup script
cat > backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup uploads
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz uploads/

# Backup database (if using PostgreSQL)
pg_dump excel_app > $BACKUP_DIR/database_$DATE.sql

# Cleanup old backups (keep last 7 days)
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
EOF

chmod +x backup.sh

# Add to crontab for daily backups
echo "0 2 * * * /path/to/backup.sh" | crontab -
```

## Monitoring

### Health Checks

```bash
# Application health
curl http://localhost:8000/health

# Service status
docker-compose ps

# Resource usage
docker stats

# Logs
docker-compose logs -f
```

### Monitoring Setup

#### Prometheus + Grafana

```yaml
# Add to docker-compose.prod.yml
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
```

#### Log Aggregation

```yaml
# Add to docker-compose.prod.yml
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.15.0
    environment:
      - discovery.type=single-node

  kibana:
    image: docker.elastic.co/kibana/kibana:7.15.0
    ports:
      - "5601:5601"
```

### Alerting

Set up alerts for:
- High CPU/memory usage
- Disk space low
- Application errors
- Service downtime

## Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Find process using port
sudo lsof -i :8000
sudo lsof -i :3000

# Kill process
sudo kill -9 <PID>
```

#### Permission Denied
```bash
# Fix Docker permissions
sudo usermod -aG docker $USER
newgrp docker

# Fix file permissions
sudo chown -R $USER:$USER uploads/ temp/ logs/
```

#### Out of Memory
```bash
# Check memory usage
free -h
docker stats

# Increase swap space
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

#### LibreOffice Issues
```bash
# Check LibreOffice installation
docker-compose exec backend libreoffice --version

# Test conversion
docker-compose exec backend libreoffice --headless --convert-to pdf test.xlsx
```

### Log Analysis

```bash
# View application logs
docker-compose logs backend | grep ERROR
docker-compose logs frontend | grep ERROR

# View system logs
journalctl -u docker
dmesg | grep -i error
```

### Performance Tuning

```bash
# Optimize Docker
echo '{"log-driver": "json-file", "log-opts": {"max-size": "10m", "max-file": "3"}}' | sudo tee /etc/docker/daemon.json
sudo systemctl restart docker

# Optimize Redis
echo 'vm.overcommit_memory = 1' | sudo tee -a /etc/sysctl.conf
sudo sysctl vm.overcommit_memory=1
```

### Backup and Recovery

```bash
# Create full backup
docker-compose exec backend tar -czf /tmp/full_backup.tar.gz uploads/ logs/
docker cp $(docker-compose ps -q backend):/tmp/full_backup.tar.gz ./

# Restore from backup
docker cp ./full_backup.tar.gz $(docker-compose ps -q backend):/tmp/
docker-compose exec backend tar -xzf /tmp/full_backup.tar.gz -C /
```

## Security Considerations

### Network Security
- Use HTTPS in production
- Configure firewall rules
- Use VPN for administrative access
- Implement rate limiting

### Application Security
- Keep dependencies updated
- Use strong passwords
- Implement proper authentication
- Validate all inputs
- Use security headers

### Container Security
- Run containers as non-root
- Use minimal base images
- Scan images for vulnerabilities
- Keep Docker updated

### Data Security
- Encrypt data at rest
- Use secure database connections
- Implement backup encryption
- Regular security audits

## Maintenance

### Regular Tasks
- Update dependencies monthly
- Review logs weekly
- Test backups monthly
- Security patches immediately
- Performance monitoring daily

### Update Procedure
```bash
# 1. Backup current deployment
./backup.sh

# 2. Pull latest changes
git pull origin main

# 3. Update dependencies
docker-compose pull

# 4. Rebuild and deploy
docker-compose -f docker-compose.prod.yml up --build -d

# 5. Verify deployment
make health
```
