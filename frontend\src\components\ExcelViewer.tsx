import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { FiRefreshCw, FiDownload, FiEdit3, FiImage, FiCalculator } from 'react-icons/fi';
import toast from 'react-hot-toast';
import { excelApi, ExcelPreviewResponse, SheetData } from '../services/api';
import SheetTabs from './SheetTabs';
import ExcelGrid from './ExcelGrid';
import FormulaBar from './FormulaBar';

interface ExcelViewerProps {
  fileId: string;
}

const ExcelViewer: React.FC<ExcelViewerProps> = ({ fileId }) => {
  const [currentSheet, setCurrentSheet] = useState(0);
  const [selectedCell, setSelectedCell] = useState<{ row: number; col: number } | null>(null);
  const queryClient = useQueryClient();

  const { data: preview, isLoading, error, refetch } = useQuery({
    queryKey: ['excel-preview', fileId, currentSheet],
    queryFn: () => excelApi.getPreview(fileId, currentSheet),
    enabled: !!fileId,
  });

  const { data: sheets = [] } = useQuery({
    queryKey: ['excel-sheets', fileId],
    queryFn: () => excelApi.getSheets(fileId),
    enabled: !!fileId,
  });

  const { data: formulas = [] } = useQuery({
    queryKey: ['excel-formulas', fileId, currentSheet],
    queryFn: () => excelApi.getFormulas(fileId, currentSheet),
    enabled: !!fileId,
  });

  const recalculateMutation = useMutation({
    mutationFn: () => excelApi.recalculateFormulas(fileId),
    onSuccess: () => {
      toast.success('Formulas recalculated successfully');
      queryClient.invalidateQueries({ queryKey: ['excel-preview', fileId] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || 'Failed to recalculate formulas');
    },
  });

  const handleSheetChange = (sheetIndex: number) => {
    setCurrentSheet(sheetIndex);
    setSelectedCell(null);
  };

  const handleCellSelect = (row: number, col: number) => {
    setSelectedCell({ row, col });
  };

  const handleRefresh = () => {
    refetch();
    queryClient.invalidateQueries({ queryKey: ['excel-sheets', fileId] });
    queryClient.invalidateQueries({ queryKey: ['excel-formulas', fileId] });
  };

  const handleRecalculate = () => {
    recalculateMutation.mutate();
  };

  if (isLoading) {
    return (
      <div className="loading">
        <div>Loading Excel file...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="error">
        <div>Failed to load Excel file</div>
        <button className="btn" onClick={handleRefresh} style={{ marginTop: '1rem' }}>
          Try Again
        </button>
      </div>
    );
  }

  if (!preview) {
    return (
      <div className="error">
        <div>Excel file not found</div>
      </div>
    );
  }

  const currentSheetData = sheets[currentSheet];
  const hasFormulas = preview.metadata.has_formulas;
  const hasImages = preview.metadata.has_images;

  return (
    <div className="excel-main">
      {/* Toolbar */}
      <div className="excel-toolbar">
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
          <h3 style={{ margin: 0 }}>{preview.metadata.filename}</h3>
          
          <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center', fontSize: '0.9rem', color: '#6c757d' }}>
            <span>{preview.metadata.total_sheets} sheets</span>
            {hasFormulas && (
              <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                <FiCalculator size={12} />
                Formulas
              </span>
            )}
            {hasImages && (
              <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                <FiImage size={12} />
                Images
              </span>
            )}
          </div>
        </div>
        
        <div style={{ display: 'flex', gap: '0.5rem' }}>
          <button
            className="btn"
            onClick={handleRefresh}
            title="Refresh"
          >
            <FiRefreshCw size={16} />
          </button>
          
          {hasFormulas && (
            <button
              className="btn"
              onClick={handleRecalculate}
              disabled={recalculateMutation.isPending}
              title="Recalculate Formulas"
            >
              <FiCalculator size={16} />
              {recalculateMutation.isPending ? 'Calculating...' : 'Recalculate'}
            </button>
          )}
        </div>
      </div>

      {/* Formula Bar */}
      {selectedCell && currentSheetData && (
        <FormulaBar
          fileId={fileId}
          sheetIndex={currentSheet}
          selectedCell={selectedCell}
          sheetData={currentSheetData}
          onCellUpdate={() => {
            queryClient.invalidateQueries({ queryKey: ['excel-preview', fileId] });
            queryClient.invalidateQueries({ queryKey: ['excel-sheets', fileId] });
          }}
        />
      )}

      {/* Excel Content */}
      <div style={{ flex: 1, display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
        {/* Grid */}
        <div style={{ flex: 1, overflow: 'hidden' }}>
          {currentSheetData ? (
            <ExcelGrid
              sheetData={currentSheetData}
              selectedCell={selectedCell}
              onCellSelect={handleCellSelect}
              fileId={fileId}
            />
          ) : preview.html_preview ? (
            <div 
              style={{ 
                height: '100%', 
                overflow: 'auto', 
                padding: '1rem',
                backgroundColor: 'white'
              }}
              dangerouslySetInnerHTML={{ __html: preview.html_preview }}
            />
          ) : (
            <div className="loading">No preview available</div>
          )}
        </div>

        {/* Sheet Tabs */}
        <SheetTabs
          sheets={sheets}
          currentSheet={currentSheet}
          onSheetChange={handleSheetChange}
        />
      </div>
    </div>
  );
};

export default ExcelViewer;
