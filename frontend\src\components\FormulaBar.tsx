import React, { useState, useEffect } from 'react';
import { useMutation } from '@tanstack/react-query';
import { FiEdit3, FiCheck, FiX } from 'react-icons/fi';
import toast from 'react-hot-toast';
import { excelApi, SheetData, CellData } from '../services/api';

interface FormulaBarProps {
  fileId: string;
  sheetIndex: number;
  selectedCell: { row: number; col: number };
  sheetData: SheetData;
  onCellUpdate: () => void;
}

const FormulaBar: React.FC<FormulaBarProps> = ({
  fileId,
  sheetIndex,
  selectedCell,
  sheetData,
  onCellUpdate,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState('');
  const [editFormula, setEditFormula] = useState('');

  // Find current cell data
  const currentCell = sheetData.cells.find(
    cell => cell.row === selectedCell.row && cell.col === selectedCell.col
  );

  const updateCellMutation = useMutation({
    mutationFn: (data: { value?: any; formula?: string }) =>
      excelApi.updateCell(fileId, {
        sheet_index: sheetIndex,
        row: selectedCell.row,
        col: selectedCell.col,
        value: data.value,
        formula: data.formula,
      }),
    onSuccess: () => {
      toast.success('Cell updated successfully');
      setIsEditing(false);
      onCellUpdate();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || 'Failed to update cell');
    },
  });

  useEffect(() => {
    if (currentCell) {
      setEditValue(currentCell.value?.toString() || '');
      setEditFormula(currentCell.formula || '');
    } else {
      setEditValue('');
      setEditFormula('');
    }
    setIsEditing(false);
  }, [selectedCell, currentCell]);

  const getColumnLetter = (col: number): string => {
    let result = '';
    while (col >= 0) {
      result = String.fromCharCode(65 + (col % 26)) + result;
      col = Math.floor(col / 26) - 1;
    }
    return result;
  };

  const getCellAddress = (): string => {
    return `${getColumnLetter(selectedCell.col)}${selectedCell.row + 1}`;
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    const hasFormula = editFormula.trim().length > 0;
    const hasValue = editValue.trim().length > 0;

    if (hasFormula) {
      updateCellMutation.mutate({ formula: editFormula });
    } else if (hasValue) {
      // Try to parse as number, otherwise keep as string
      const numValue = parseFloat(editValue);
      const finalValue = !isNaN(numValue) && isFinite(numValue) ? numValue : editValue;
      updateCellMutation.mutate({ value: finalValue });
    } else {
      updateCellMutation.mutate({ value: null });
    }
  };

  const handleCancel = () => {
    if (currentCell) {
      setEditValue(currentCell.value?.toString() || '');
      setEditFormula(currentCell.formula || '');
    } else {
      setEditValue('');
      setEditFormula('');
    }
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      padding: '0.5rem 1rem',
      backgroundColor: '#f8f9fa',
      borderBottom: '1px solid #dee2e6',
      gap: '1rem',
    }}>
      {/* Cell Address */}
      <div style={{
        minWidth: '80px',
        padding: '0.25rem 0.5rem',
        backgroundColor: 'white',
        border: '1px solid #dee2e6',
        borderRadius: '4px',
        fontFamily: 'monospace',
        fontSize: '0.9rem',
        fontWeight: 'bold',
      }}>
        {getCellAddress()}
      </div>

      {/* Formula/Value Input */}
      <div style={{ flex: 1, display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
        {isEditing ? (
          <>
            {/* Formula Input */}
            <div style={{ flex: 1 }}>
              <label style={{ fontSize: '0.8rem', color: '#6c757d', marginBottom: '0.25rem', display: 'block' }}>
                Formula (optional):
              </label>
              <input
                type="text"
                value={editFormula}
                onChange={(e) => setEditFormula(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="=SUM(A1:A10)"
                style={{
                  width: '100%',
                  padding: '0.25rem 0.5rem',
                  border: '1px solid #dee2e6',
                  borderRadius: '4px',
                  fontFamily: 'monospace',
                  fontSize: '0.9rem',
                }}
              />
            </div>

            {/* Value Input */}
            <div style={{ flex: 1 }}>
              <label style={{ fontSize: '0.8rem', color: '#6c757d', marginBottom: '0.25rem', display: 'block' }}>
                Value:
              </label>
              <input
                type="text"
                value={editValue}
                onChange={(e) => setEditValue(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Enter value"
                disabled={editFormula.trim().length > 0}
                style={{
                  width: '100%',
                  padding: '0.25rem 0.5rem',
                  border: '1px solid #dee2e6',
                  borderRadius: '4px',
                  fontSize: '0.9rem',
                  opacity: editFormula.trim().length > 0 ? 0.5 : 1,
                }}
              />
            </div>

            {/* Action Buttons */}
            <div style={{ display: 'flex', gap: '0.25rem' }}>
              <button
                className="btn btn-primary"
                onClick={handleSave}
                disabled={updateCellMutation.isPending}
                style={{ padding: '0.25rem 0.5rem' }}
              >
                <FiCheck size={14} />
              </button>
              <button
                className="btn"
                onClick={handleCancel}
                disabled={updateCellMutation.isPending}
                style={{ padding: '0.25rem 0.5rem' }}
              >
                <FiX size={14} />
              </button>
            </div>
          </>
        ) : (
          <>
            {/* Display Mode */}
            <div style={{ flex: 1, display: 'flex', alignItems: 'center', gap: '1rem' }}>
              {currentCell?.formula && (
                <div style={{ flex: 1 }}>
                  <span style={{ fontSize: '0.8rem', color: '#6c757d' }}>Formula: </span>
                  <code style={{ 
                    backgroundColor: '#e9ecef', 
                    padding: '0.25rem 0.5rem', 
                    borderRadius: '4px',
                    fontSize: '0.9rem'
                  }}>
                    {currentCell.formula}
                  </code>
                </div>
              )}
              
              <div style={{ flex: 1 }}>
                <span style={{ fontSize: '0.8rem', color: '#6c757d' }}>Value: </span>
                <span style={{ fontSize: '0.9rem' }}>
                  {currentCell?.value?.toString() || <em style={{ color: '#6c757d' }}>Empty</em>}
                </span>
              </div>
            </div>

            <button
              className="btn"
              onClick={handleEdit}
              style={{ padding: '0.25rem 0.5rem', display: 'flex', alignItems: 'center', gap: '0.25rem' }}
            >
              <FiEdit3 size={14} />
              Edit
            </button>
          </>
        )}
      </div>

      {/* Cell Type Info */}
      {currentCell && (
        <div style={{
          fontSize: '0.8rem',
          color: '#6c757d',
          padding: '0.25rem 0.5rem',
          backgroundColor: '#e9ecef',
          borderRadius: '4px',
        }}>
          {currentCell.cell_type}
        </div>
      )}
    </div>
  );
};

export default FormulaBar;
