"""
Background cleanup service for temporary files and cache management.
"""

import asyncio
import threading
from typing import Optional
import structlog

from app.core.config import settings
from app.services.file_service import file_service

logger = structlog.get_logger(__name__)


class CleanupService:
    """Background service for cleaning up temporary files and managing cache."""
    
    def __init__(self):
        self.cleanup_task: Optional[asyncio.Task] = None
        self.running = False
        self._loop: Optional[asyncio.AbstractEventLoop] = None
        self._thread: Optional[threading.Thread] = None
    
    def start(self):
        """Start the cleanup service."""
        if self.running:
            logger.warning("Cleanup service is already running")
            return
        
        self.running = True
        self._thread = threading.Thread(target=self._run_in_thread, daemon=True)
        self._thread.start()
        logger.info("Cleanup service started")
    
    def stop(self):
        """Stop the cleanup service."""
        if not self.running:
            return
        
        self.running = False
        
        if self.cleanup_task and not self.cleanup_task.done():
            self.cleanup_task.cancel()
        
        if self._thread and self._thread.is_alive():
            self._thread.join(timeout=5)
        
        logger.info("Cleanup service stopped")
    
    def _run_in_thread(self):
        """Run the cleanup service in a separate thread."""
        try:
            # Create new event loop for this thread
            self._loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self._loop)
            
            # Start the cleanup task
            self.cleanup_task = self._loop.create_task(self._cleanup_loop())
            
            # Run the event loop
            self._loop.run_until_complete(self.cleanup_task)
            
        except asyncio.CancelledError:
            logger.info("Cleanup service cancelled")
        except Exception as e:
            logger.error("Cleanup service error", error=str(e))
        finally:
            if self._loop:
                self._loop.close()
    
    async def _cleanup_loop(self):
        """Main cleanup loop."""
        while self.running:
            try:
                await self._perform_cleanup()
                await asyncio.sleep(settings.CLEANUP_INTERVAL)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("Error during cleanup", error=str(e))
                await asyncio.sleep(60)  # Wait a minute before retrying
    
    async def _perform_cleanup(self):
        """Perform cleanup operations."""
        try:
            # Clean up temporary files
            cleaned_files = await file_service.cleanup_temp_files()
            
            if cleaned_files > 0:
                logger.info("Cleanup completed", temp_files_cleaned=cleaned_files)
            
            # Additional cleanup operations can be added here
            # For example:
            # - Clean up old conversion cache entries
            # - Remove orphaned metadata files
            # - Clean up old log files
            
        except Exception as e:
            logger.error("Cleanup operation failed", error=str(e))
    
    async def force_cleanup(self):
        """Force immediate cleanup (useful for testing or manual triggers)."""
        if not self.running:
            logger.warning("Cleanup service is not running")
            return
        
        await self._perform_cleanup()
        logger.info("Force cleanup completed")


# Global cleanup service instance
cleanup_service = CleanupService()
