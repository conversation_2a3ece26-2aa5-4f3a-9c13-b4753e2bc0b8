import React from 'react';
import { FiEye, FiEyeOff } from 'react-icons/fi';
import { SheetData } from '../services/api';

interface SheetTabsProps {
  sheets: SheetData[];
  currentSheet: number;
  onSheetChange: (sheetIndex: number) => void;
}

const SheetTabs: React.FC<SheetTabsProps> = ({ sheets, currentSheet, onSheetChange }) => {
  if (sheets.length === 0) {
    return null;
  }

  return (
    <div className="excel-sheets">
      {sheets.map((sheet, index) => (
        <button
          key={sheet.index}
          className={`sheet-tab ${index === currentSheet ? 'active' : ''}`}
          onClick={() => onSheetChange(index)}
          disabled={sheet.hidden}
          style={{
            opacity: sheet.hidden ? 0.5 : 1,
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
          }}
          title={sheet.hidden ? 'Hidden sheet' : `Switch to ${sheet.name}`}
        >
          {sheet.hidden && <FiEyeOff size={12} />}
          <span>{sheet.name}</span>
          
          {/* Sheet info */}
          <span style={{ 
            fontSize: '0.7rem', 
            opacity: 0.7,
            marginLeft: '0.25rem'
          }}>
            ({sheet.row_count}×{sheet.col_count})
          </span>
          
          {/* Indicators */}
          <div style={{ display: 'flex', gap: '0.25rem' }}>
            {sheet.images.length > 0 && (
              <span 
                style={{ 
                  fontSize: '0.7rem', 
                  backgroundColor: '#17a2b8', 
                  color: 'white', 
                  padding: '0.1rem 0.3rem', 
                  borderRadius: '2px' 
                }}
                title={`${sheet.images.length} images`}
              >
                📷
              </span>
            )}
            
            {sheet.cells.some(cell => cell.formula) && (
              <span 
                style={{ 
                  fontSize: '0.7rem', 
                  backgroundColor: '#28a745', 
                  color: 'white', 
                  padding: '0.1rem 0.3rem', 
                  borderRadius: '2px' 
                }}
                title="Contains formulas"
              >
                ƒ
              </span>
            )}
          </div>
        </button>
      ))}
    </div>
  );
};

export default SheetTabs;
