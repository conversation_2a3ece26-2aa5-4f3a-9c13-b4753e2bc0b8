"""
LibreOffice utilities for Excel processing using headless mode and PyUNO.
"""

import asyncio
import os
import subprocess
import tempfile
import uuid
import base64
from pathlib import Path
from typing import Dict, List, Optional, Any
import structlog

from app.core.config import settings

logger = structlog.get_logger(__name__)


class LibreOfficeConverter:
    """LibreOffice converter for Excel files."""
    
    def __init__(self):
        self.libreoffice_path = settings.LIBREOFFICE_PATH
        self.timeout = settings.LIBREOFFICE_TIMEOUT
        self.temp_dir = Path(settings.TEMP_DIR)
    
    async def convert_to_html(self, file_path: str, sheet_index: int = 0) -> Optional[str]:
        """Convert Excel file to HTML using LibreOffice."""
        try:
            # Create temporary output directory
            with tempfile.TemporaryDirectory(dir=self.temp_dir) as temp_dir:
                output_dir = Path(temp_dir)
                
                # Build LibreOffice command
                cmd = [
                    self.libreoffice_path,
                    "--headless",
                    "--convert-to", "html",
                    "--outdir", str(output_dir),
                    file_path
                ]
                
                # Run LibreOffice conversion
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                try:
                    stdout, stderr = await asyncio.wait_for(
                        process.communicate(),
                        timeout=self.timeout
                    )
                except asyncio.TimeoutError:
                    process.kill()
                    await process.wait()
                    logger.error("LibreOffice conversion timeout", file_path=file_path)
                    return None
                
                if process.returncode != 0:
                    logger.error(
                        "LibreOffice conversion failed",
                        file_path=file_path,
                        returncode=process.returncode,
                        stderr=stderr.decode()
                    )
                    return None
                
                # Find the generated HTML file
                input_filename = Path(file_path).stem
                html_file = output_dir / f"{input_filename}.html"
                
                if html_file.exists():
                    async with asyncio.to_thread(open, html_file, 'r', encoding='utf-8') as f:
                        content = await asyncio.to_thread(f.read)
                    
                    # Post-process HTML to make it more web-friendly
                    processed_content = await self._process_html_content(content)
                    return processed_content
                else:
                    logger.error("HTML output file not found", expected_path=str(html_file))
                    return None
                    
        except Exception as e:
            logger.error("HTML conversion error", file_path=file_path, error=str(e))
            return None
    
    async def _process_html_content(self, html_content: str) -> str:
        """Post-process HTML content for better web display."""
        try:
            # Add responsive CSS and Excel-like styling
            css_styles = """
            <style>
            body { 
                font-family: Arial, sans-serif; 
                margin: 0; 
                padding: 20px; 
                background: #f5f5f5; 
            }
            table { 
                border-collapse: collapse; 
                background: white; 
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                margin: 0 auto;
            }
            td, th { 
                border: 1px solid #ddd; 
                padding: 8px; 
                text-align: left; 
                min-width: 80px;
                position: relative;
            }
            th { 
                background-color: #f8f9fa; 
                font-weight: bold; 
            }
            tr:nth-child(even) { 
                background-color: #f9f9f9; 
            }
            tr:hover { 
                background-color: #e8f4f8; 
            }
            .excel-container {
                overflow: auto;
                max-width: 100%;
                max-height: 80vh;
            }
            .cell-formula {
                font-style: italic;
                color: #007bff;
            }
            .cell-number {
                text-align: right;
            }
            </style>
            """
            
            # Insert CSS into HTML head
            if "<head>" in html_content:
                html_content = html_content.replace("<head>", f"<head>{css_styles}")
            else:
                html_content = f"<html><head>{css_styles}</head><body>{html_content}</body></html>"
            
            # Wrap tables in scrollable container
            html_content = html_content.replace(
                "<table",
                '<div class="excel-container"><table'
            )
            html_content = html_content.replace(
                "</table>",
                "</table></div>"
            )
            
            return html_content
            
        except Exception as e:
            logger.error("HTML processing error", error=str(e))
            return html_content
    
    async def extract_data_with_uno(self, file_path: str) -> Dict[str, Any]:
        """Extract data using PyUNO API (placeholder implementation)."""
        try:
            # This is a placeholder implementation
            # In a real implementation, you would use PyUNO to:
            # 1. Open the document
            # 2. Iterate through sheets
            # 3. Extract cell data, formulas, and formatting
            # 4. Extract images and their positions
            # 5. Return structured data
            
            # For now, we'll return mock data structure
            sheets_data = await self._extract_basic_data(file_path)
            
            return {
                "sheets": sheets_data,
                "document_properties": {
                    "title": "",
                    "author": "",
                    "created": "",
                    "modified": ""
                }
            }
            
        except Exception as e:
            logger.error("PyUNO data extraction error", file_path=file_path, error=str(e))
            return {"sheets": []}
    
    async def _extract_basic_data(self, file_path: str) -> List[Dict[str, Any]]:
        """Extract basic data using openpyxl as fallback."""
        try:
            import openpyxl
            from openpyxl.utils import get_column_letter
            
            # Load workbook
            workbook = await asyncio.to_thread(openpyxl.load_workbook, file_path, data_only=False)
            sheets_data = []
            
            for sheet_index, sheet_name in enumerate(workbook.sheetnames):
                worksheet = workbook[sheet_name]
                
                # Extract cells
                cells = []
                max_row = worksheet.max_row or 1
                max_col = worksheet.max_column or 1
                
                for row in range(1, min(max_row + 1, 1000)):  # Limit to 1000 rows
                    for col in range(1, min(max_col + 1, 100)):  # Limit to 100 columns
                        cell = worksheet.cell(row=row, column=col)
                        
                        if cell.value is not None or cell.formula:
                            cell_type = self._determine_cell_type(cell)
                            
                            cells.append({
                                "row": row - 1,  # Convert to 0-based
                                "col": col - 1,  # Convert to 0-based
                                "value": cell.value,
                                "formula": cell.formula if cell.formula else None,
                                "cell_type": cell_type,
                                "format": str(cell.number_format) if cell.number_format else None,
                                "style": self._extract_cell_style(cell)
                            })
                
                # Extract images (placeholder)
                images = await self._extract_images_from_sheet(worksheet, sheet_index)
                
                sheet_data = {
                    "index": sheet_index,
                    "name": sheet_name,
                    "cells": cells,
                    "images": images,
                    "row_count": max_row,
                    "col_count": max_col,
                    "hidden": worksheet.sheet_state == 'hidden'
                }
                
                sheets_data.append(sheet_data)
            
            workbook.close()
            return sheets_data
            
        except Exception as e:
            logger.error("Basic data extraction error", file_path=file_path, error=str(e))
            return []
    
    def _determine_cell_type(self, cell) -> str:
        """Determine cell type based on value and formula."""
        if cell.formula:
            return "formula"
        elif cell.value is None:
            return "empty"
        elif isinstance(cell.value, bool):
            return "boolean"
        elif isinstance(cell.value, (int, float)):
            return "number"
        elif hasattr(cell.value, 'date'):  # datetime object
            return "date"
        else:
            return "text"
    
    def _extract_cell_style(self, cell) -> Dict[str, Any]:
        """Extract cell styling information."""
        try:
            style = {}
            
            if cell.font:
                style["font"] = {
                    "name": cell.font.name,
                    "size": cell.font.size,
                    "bold": cell.font.bold,
                    "italic": cell.font.italic,
                    "color": str(cell.font.color.rgb) if cell.font.color else None
                }
            
            if cell.fill:
                style["fill"] = {
                    "type": cell.fill.fill_type,
                    "color": str(cell.fill.start_color.rgb) if cell.fill.start_color else None
                }
            
            if cell.alignment:
                style["alignment"] = {
                    "horizontal": cell.alignment.horizontal,
                    "vertical": cell.alignment.vertical,
                    "wrap_text": cell.alignment.wrap_text
                }
            
            return style
            
        except Exception as e:
            logger.error("Style extraction error", error=str(e))
            return {}
    
    async def _extract_images_from_sheet(self, worksheet, sheet_index: int) -> List[Dict[str, Any]]:
        """Extract images from worksheet (placeholder implementation)."""
        try:
            images = []
            
            # This is a placeholder - in a real implementation you would:
            # 1. Access worksheet._images or similar
            # 2. Extract image data, position, and size
            # 3. Convert to base64 or save to file
            # 4. Return structured image data
            
            # For now, return empty list
            return images
            
        except Exception as e:
            logger.error("Image extraction error", sheet_index=sheet_index, error=str(e))
            return []
    
    async def save_document(self, file_path: str, output_path: str) -> bool:
        """Save document using LibreOffice (for modifications)."""
        try:
            cmd = [
                self.libreoffice_path,
                "--headless",
                "--convert-to", "xlsx",
                "--outdir", str(Path(output_path).parent),
                file_path
            ]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=self.timeout
                )
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                return False
            
            return process.returncode == 0
            
        except Exception as e:
            logger.error("Document save error", file_path=file_path, error=str(e))
            return False
