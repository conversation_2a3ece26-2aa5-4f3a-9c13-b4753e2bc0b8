import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import FileUpload from '../components/FileUpload';
import FileList from '../components/FileList';

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'upload' | 'files'>('upload');

  const handleUploadSuccess = (fileId: string) => {
    // Switch to files tab to show the uploaded file
    setActiveTab('files');
    
    // Navigate to the file after a short delay to allow processing to start
    setTimeout(() => {
      navigate(`/excel/${fileId}`);
    }, 2000);
  };

  const handleFileSelect = (fileId: string) => {
    navigate(`/excel/${fileId}`);
  };

  return (
    <div className="excel-content">
      {/* Sidebar */}
      <div className="excel-sidebar">
        <h3 style={{ marginTop: 0 }}>Navigation</h3>
        
        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
          <button
            className={`btn ${activeTab === 'upload' ? 'btn-primary' : ''}`}
            onClick={() => setActiveTab('upload')}
            style={{ justifyContent: 'flex-start' }}
          >
            📤 Upload File
          </button>
          
          <button
            className={`btn ${activeTab === 'files' ? 'btn-primary' : ''}`}
            onClick={() => setActiveTab('files')}
            style={{ justifyContent: 'flex-start' }}
          >
            📁 My Files
          </button>
        </div>

        <hr style={{ margin: '1rem 0' }} />

        <div style={{ fontSize: '0.9rem', color: '#6c757d' }}>
          <h4>Features</h4>
          <ul style={{ paddingLeft: '1rem', lineHeight: '1.6' }}>
            <li>Upload Excel files (.xls, .xlsx)</li>
            <li>Preview in browser</li>
            <li>Edit cells and formulas</li>
            <li>View multiple sheets</li>
            <li>Extract formulas</li>
            <li>Manage images</li>
          </ul>
        </div>

        <hr style={{ margin: '1rem 0' }} />

        <div style={{ fontSize: '0.8rem', color: '#6c757d' }}>
          <h5>Supported Formats</h5>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>
            <span>• Excel 97-2003 (.xls)</span>
            <span>• Excel 2007+ (.xlsx)</span>
            <span>• Max size: 100MB</span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="excel-main">
        {activeTab === 'upload' && (
          <div style={{ padding: '2rem' }}>
            <div style={{ maxWidth: '600px', margin: '0 auto' }}>
              <h2 style={{ textAlign: 'center', marginBottom: '2rem' }}>
                Upload Excel File
              </h2>
              
              <FileUpload onUploadSuccess={handleUploadSuccess} />
              
              <div style={{ 
                marginTop: '2rem', 
                padding: '1rem', 
                backgroundColor: '#f8f9fa', 
                borderRadius: '8px',
                fontSize: '0.9rem',
                color: '#6c757d'
              }}>
                <h4 style={{ marginTop: 0, color: '#495057' }}>How it works:</h4>
                <ol style={{ paddingLeft: '1.5rem', lineHeight: '1.6' }}>
                  <li>Upload your Excel file using the area above</li>
                  <li>Wait for processing to complete (usually takes a few seconds)</li>
                  <li>View and edit your file in the browser</li>
                  <li>Navigate between sheets using the tabs at the bottom</li>
                  <li>Click on cells to edit values or formulas</li>
                </ol>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'files' && (
          <div>
            <FileList onFileSelect={handleFileSelect} />
          </div>
        )}
      </div>
    </div>
  );
};

export default HomePage;
