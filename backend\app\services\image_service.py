"""
Image management service for Excel files.
"""

import base64
import io
import uuid
from pathlib import Path
from typing import List, Optional, Dict, Any, NamedTuple
import aiofiles
from PIL import Image
import structlog

from app.core.config import settings
from app.models.excel import ImageData, ImageUploadRequest, ImageType
from app.services.file_service import file_service

logger = structlog.get_logger(__name__)


class ImageStream(NamedTuple):
    """Image stream data."""
    stream: io.BytesIO
    content_type: str
    filename: str


class ImageService:
    """Service for image management in Excel files."""
    
    def __init__(self):
        self.images_dir = Path(settings.UPLOAD_DIR) / "images"
    
    async def initialize(self):
        """Initialize image service."""
        self.images_dir.mkdir(parents=True, exist_ok=True)
    
    async def get_images(self, file_id: str, sheet_index: Optional[int] = None) -> List[ImageData]:
        """Get all images from a file or specific sheet."""
        try:
            metadata = await file_service.get_metadata(file_id)
            if not metadata:
                return []
            
            images = []
            for sheet in metadata.sheets:
                if sheet_index is None or sheet["index"] == sheet_index:
                    for image_data in sheet.get("images", []):
                        images.append(ImageData(**image_data))
            
            return images
            
        except Exception as e:
            logger.error("Failed to get images", file_id=file_id, error=str(e))
            return []
    
    async def get_image_stream(self, file_id: str, image_id: str) -> Optional[ImageStream]:
        """Get image stream for download."""
        try:
            # Find image in metadata
            images = await self.get_images(file_id)
            image_data = None
            
            for img in images:
                if img.id == image_id:
                    image_data = img
                    break
            
            if not image_data:
                return None
            
            # If image has base64 data, decode it
            if image_data.data:
                image_bytes = base64.b64decode(image_data.data)
                stream = io.BytesIO(image_bytes)
                
                # Determine content type from image data
                content_type = self._get_content_type_from_data(image_bytes)
                
                return ImageStream(
                    stream=stream,
                    content_type=content_type,
                    filename=image_data.name
                )
            
            # If image has URL, try to load from file
            if image_data.url:
                image_path = self.images_dir / file_id / image_data.url
                if image_path.exists():
                    async with aiofiles.open(image_path, 'rb') as f:
                        image_bytes = await f.read()
                    
                    stream = io.BytesIO(image_bytes)
                    content_type = self._get_content_type_from_extension(image_path.suffix)
                    
                    return ImageStream(
                        stream=stream,
                        content_type=content_type,
                        filename=image_data.name
                    )
            
            return None
            
        except Exception as e:
            logger.error("Failed to get image stream", file_id=file_id, image_id=image_id, error=str(e))
            return None
    
    async def upload_image(
        self, 
        file_id: str, 
        image_file, 
        upload_request: ImageUploadRequest
    ) -> Optional[ImageData]:
        """Upload and insert a new image into the Excel file."""
        try:
            # Validate file exists
            metadata = await file_service.get_metadata(file_id)
            if not metadata:
                return None
            
            # Validate sheet index
            if upload_request.sheet_index >= len(metadata.sheets):
                return None
            
            # Generate unique image ID
            image_id = str(uuid.uuid4())
            
            # Read and process image
            image_content = await image_file.read()
            
            # Validate image
            try:
                with Image.open(io.BytesIO(image_content)) as img:
                    original_size = img.size
                    image_format = img.format.lower()
            except Exception as e:
                logger.error("Invalid image file", error=str(e))
                return None
            
            # Determine final size
            final_size = upload_request.size or {
                "width": original_size[0],
                "height": original_size[1]
            }
            
            # Save image to disk
            image_dir = self.images_dir / file_id
            image_dir.mkdir(parents=True, exist_ok=True)
            
            image_filename = f"{image_id}.{image_format}"
            image_path = image_dir / image_filename
            
            async with aiofiles.open(image_path, 'wb') as f:
                await f.write(image_content)
            
            # Create image data
            image_data = ImageData(
                id=image_id,
                name=image_file.filename or f"image_{image_id}",
                image_type=upload_request.image_type,
                position=upload_request.position,
                size=final_size,
                url=image_filename,
                sheet_index=upload_request.sheet_index
            )
            
            # Update metadata
            sheet = metadata.sheets[upload_request.sheet_index]
            if "images" not in sheet:
                sheet["images"] = []
            sheet["images"].append(image_data.model_dump())
            
            # Update has_images flag
            await file_service.update_metadata(file_id, {
                "sheets": metadata.sheets,
                "has_images": True
            })
            
            logger.info("Image uploaded successfully", file_id=file_id, image_id=image_id)
            return image_data
            
        except Exception as e:
            logger.error("Failed to upload image", file_id=file_id, error=str(e))
            return None
    
    async def update_image(
        self, 
        file_id: str, 
        image_id: str, 
        update_data: Dict[str, Any]
    ) -> Optional[ImageData]:
        """Update image position and size."""
        try:
            metadata = await file_service.get_metadata(file_id)
            if not metadata:
                return None
            
            # Find and update image
            updated_image = None
            for sheet in metadata.sheets:
                for i, image_data in enumerate(sheet.get("images", [])):
                    if image_data["id"] == image_id:
                        # Update image data
                        if "position" in update_data:
                            image_data["position"] = update_data["position"]
                        if "size" in update_data:
                            image_data["size"] = update_data["size"]
                        
                        updated_image = ImageData(**image_data)
                        break
                
                if updated_image:
                    break
            
            if not updated_image:
                return None
            
            # Save updated metadata
            await file_service.update_metadata(file_id, {"sheets": metadata.sheets})
            
            logger.info("Image updated successfully", file_id=file_id, image_id=image_id)
            return updated_image
            
        except Exception as e:
            logger.error("Failed to update image", file_id=file_id, image_id=image_id, error=str(e))
            return None
    
    async def delete_image(self, file_id: str, image_id: str) -> bool:
        """Delete an image from the Excel file."""
        try:
            metadata = await file_service.get_metadata(file_id)
            if not metadata:
                return False
            
            # Find and remove image
            image_found = False
            image_filename = None
            
            for sheet in metadata.sheets:
                images = sheet.get("images", [])
                for i, image_data in enumerate(images):
                    if image_data["id"] == image_id:
                        image_filename = image_data.get("url")
                        images.pop(i)
                        image_found = True
                        break
                
                if image_found:
                    break
            
            if not image_found:
                return False
            
            # Delete image file
            if image_filename:
                image_path = self.images_dir / file_id / image_filename
                if image_path.exists():
                    image_path.unlink()
            
            # Update metadata
            await file_service.update_metadata(file_id, {"sheets": metadata.sheets})
            
            logger.info("Image deleted successfully", file_id=file_id, image_id=image_id)
            return True
            
        except Exception as e:
            logger.error("Failed to delete image", file_id=file_id, image_id=image_id, error=str(e))
            return False
    
    def _get_content_type_from_data(self, image_data: bytes) -> str:
        """Determine content type from image data."""
        if image_data.startswith(b'\xff\xd8\xff'):
            return 'image/jpeg'
        elif image_data.startswith(b'\x89PNG\r\n\x1a\n'):
            return 'image/png'
        elif image_data.startswith(b'GIF87a') or image_data.startswith(b'GIF89a'):
            return 'image/gif'
        elif image_data.startswith(b'RIFF') and b'WEBP' in image_data[:12]:
            return 'image/webp'
        else:
            return 'image/jpeg'  # Default fallback
    
    def _get_content_type_from_extension(self, extension: str) -> str:
        """Determine content type from file extension."""
        extension = extension.lower()
        content_types = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.webp': 'image/webp',
            '.bmp': 'image/bmp',
            '.tiff': 'image/tiff',
            '.svg': 'image/svg+xml'
        }
        return content_types.get(extension, 'image/jpeg')


# Global image service instance
image_service = ImageService()
