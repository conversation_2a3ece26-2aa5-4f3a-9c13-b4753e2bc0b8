"""
Pytest configuration and fixtures.
"""

import asyncio
import os
import tempfile
from pathlib import Path
from typing import AsyncGenerator, Generator
import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from httpx import AsyncClient

from app.main import app
from app.core.config import settings
from app.core.redis import redis_client
from app.services.file_service import file_service


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def client() -> Generator[TestClient, None, None]:
    """Create a test client for the FastAPI app."""
    with TestClient(app) as test_client:
        yield test_client


@pytest_asyncio.fixture
async def async_client() -> AsyncGenerator[AsyncClient, None]:
    """Create an async test client for the FastAPI app."""
    async with Async<PERSON><PERSON>(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """Create a temporary directory for tests."""
    with tempfile.TemporaryDirectory() as tmp_dir:
        yield Path(tmp_dir)


@pytest.fixture
def sample_excel_file(temp_dir: Path) -> Path:
    """Create a sample Excel file for testing."""
    import openpyxl
    
    # Create a simple Excel file
    workbook = openpyxl.Workbook()
    worksheet = workbook.active
    worksheet.title = "Test Sheet"
    
    # Add some test data
    worksheet['A1'] = "Name"
    worksheet['B1'] = "Age"
    worksheet['C1'] = "City"
    
    worksheet['A2'] = "John Doe"
    worksheet['B2'] = 30
    worksheet['C2'] = "New York"
    
    worksheet['A3'] = "Jane Smith"
    worksheet['B3'] = 25
    worksheet['C3'] = "Los Angeles"
    
    # Add a formula
    worksheet['D1'] = "Average Age"
    worksheet['D2'] = "=AVERAGE(B2:B3)"
    
    file_path = temp_dir / "test_file.xlsx"
    workbook.save(file_path)
    workbook.close()
    
    return file_path


@pytest.fixture
def sample_excel_with_images(temp_dir: Path) -> Path:
    """Create a sample Excel file with images for testing."""
    import openpyxl
    from PIL import Image
    
    # Create a simple Excel file
    workbook = openpyxl.Workbook()
    worksheet = workbook.active
    worksheet.title = "Test Sheet with Images"
    
    # Add some test data
    worksheet['A1'] = "Product"
    worksheet['B1'] = "Price"
    
    worksheet['A2'] = "Widget A"
    worksheet['B2'] = 19.99
    
    # Create a simple test image
    img = Image.new('RGB', (100, 100), color='red')
    img_path = temp_dir / "test_image.png"
    img.save(img_path)
    
    # Note: Adding images to Excel files programmatically is complex
    # In a real test, you might use a pre-created file with images
    
    file_path = temp_dir / "test_file_with_images.xlsx"
    workbook.save(file_path)
    workbook.close()
    
    return file_path


@pytest.fixture
def mock_redis():
    """Mock Redis client for testing."""
    class MockRedis:
        def __init__(self):
            self.data = {}
        
        async def get(self, key: str):
            return self.data.get(key)
        
        async def set(self, key: str, value: str, ex: int = None):
            self.data[key] = value
            return True
        
        async def delete(self, key: str):
            return self.data.pop(key, None) is not None
        
        async def exists(self, key: str):
            return key in self.data
        
        async def ping(self):
            return True
        
        async def close(self):
            pass
    
    return MockRedis()


@pytest_asyncio.fixture
async def setup_test_environment(temp_dir: Path, mock_redis):
    """Set up test environment with temporary directories and mock services."""
    # Override settings for testing
    original_upload_dir = settings.UPLOAD_DIR
    original_temp_dir = settings.TEMP_DIR
    
    settings.UPLOAD_DIR = str(temp_dir / "uploads")
    settings.TEMP_DIR = str(temp_dir / "temp")
    
    # Create test directories
    Path(settings.UPLOAD_DIR).mkdir(parents=True, exist_ok=True)
    Path(settings.TEMP_DIR).mkdir(parents=True, exist_ok=True)
    
    # Initialize file service with test directories
    await file_service.initialize()
    
    # Mock Redis
    original_redis = redis_client.redis
    redis_client.redis = mock_redis
    
    yield
    
    # Restore original settings
    settings.UPLOAD_DIR = original_upload_dir
    settings.TEMP_DIR = original_temp_dir
    redis_client.redis = original_redis


@pytest.fixture
def sample_file_metadata():
    """Sample file metadata for testing."""
    from app.models.excel import ExcelFileMetadata, FileStatus, SheetData, CellData
    
    return ExcelFileMetadata(
        file_id="test-file-id",
        filename="test.xlsx",
        file_size=1024,
        file_hash="abc123",
        mime_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        status=FileStatus.READY,
        sheets=[
            SheetData(
                index=0,
                name="Sheet1",
                cells=[
                    CellData(row=0, col=0, value="Test", cell_type="text"),
                    CellData(row=0, col=1, value=123, cell_type="number"),
                ],
                images=[],
                row_count=1,
                col_count=2,
                hidden=False
            )
        ],
        total_sheets=1,
        has_formulas=False,
        has_images=False
    )


@pytest.fixture
def sample_upload_file():
    """Create a sample upload file for testing."""
    from fastapi import UploadFile
    import io
    
    # Create file content
    content = b"Sample Excel file content"
    file_obj = io.BytesIO(content)
    
    return UploadFile(
        filename="test.xlsx",
        file=file_obj,
        size=len(content),
        headers={"content-type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}
    )


# Test data constants
TEST_FILE_ID = "test-file-123"
TEST_SHEET_INDEX = 0
TEST_CELL_ROW = 0
TEST_CELL_COL = 0
