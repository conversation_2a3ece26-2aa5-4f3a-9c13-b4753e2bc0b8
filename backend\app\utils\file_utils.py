"""
File utility functions for validation, hashing, and management.
"""

import hashlib
import magic
import os
from pathlib import Path
from typing import NamedTuple
import aiofiles
import structlog

from app.core.config import settings

logger = structlog.get_logger(__name__)


class FileValidationResult(NamedTuple):
    """Result of file validation."""
    is_valid: bool
    error_message: str = ""


async def validate_file(file) -> FileValidationResult:
    """Validate uploaded file."""
    try:
        # Check file size
        if file.size > settings.max_file_size_bytes:
            return FileValidationResult(
                False, 
                f"File size ({file.size} bytes) exceeds maximum allowed size ({settings.max_file_size_bytes} bytes)"
            )
        
        # Check file extension
        file_extension = Path(file.filename).suffix.lower()
        if file_extension not in settings.ALLOWED_EXTENSIONS:
            return FileValidationResult(
                False,
                f"File extension '{file_extension}' is not allowed. Allowed extensions: {', '.join(settings.ALLOWED_EXTENSIONS)}"
            )
        
        # Check MIME type
        if not file.content_type or not any(
            allowed in file.content_type.lower() 
            for allowed in ['spreadsheet', 'excel', 'ms-excel', 'officedocument']
        ):
            return FileValidationResult(
                False,
                f"Invalid file type. Expected Excel file, got: {file.content_type}"
            )
        
        return FileValidationResult(True)
        
    except Exception as e:
        logger.error("File validation error", error=str(e))
        return FileValidationResult(False, "File validation failed")


async def calculate_file_hash(file_path: str, algorithm: str = "sha256") -> str:
    """Calculate hash of a file."""
    try:
        hash_obj = hashlib.new(algorithm)
        
        async with aiofiles.open(file_path, 'rb') as f:
            while chunk := await f.read(8192):
                hash_obj.update(chunk)
        
        return hash_obj.hexdigest()
        
    except Exception as e:
        logger.error("Failed to calculate file hash", file_path=file_path, error=str(e))
        raise


async def get_file_mime_type(file_path: str) -> str:
    """Get MIME type of a file."""
    try:
        mime = magic.Magic(mime=True)
        return mime.from_file(file_path)
    except Exception as e:
        logger.error("Failed to get MIME type", file_path=file_path, error=str(e))
        return "application/octet-stream"


async def ensure_directory_exists(directory: Path) -> None:
    """Ensure directory exists, create if it doesn't."""
    try:
        directory.mkdir(parents=True, exist_ok=True)
    except Exception as e:
        logger.error("Failed to create directory", directory=str(directory), error=str(e))
        raise


def get_file_size(file_path: str) -> int:
    """Get file size in bytes."""
    try:
        return os.path.getsize(file_path)
    except Exception as e:
        logger.error("Failed to get file size", file_path=file_path, error=str(e))
        return 0


def is_excel_file(filename: str) -> bool:
    """Check if filename indicates an Excel file."""
    excel_extensions = ['.xls', '.xlsx', '.xlsm', '.xlsb']
    return Path(filename).suffix.lower() in excel_extensions


async def safe_remove_file(file_path: str) -> bool:
    """Safely remove a file."""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            return True
        return False
    except Exception as e:
        logger.error("Failed to remove file", file_path=file_path, error=str(e))
        return False


def generate_temp_filename(prefix: str = "temp", suffix: str = "") -> str:
    """Generate a unique temporary filename."""
    import uuid
    unique_id = str(uuid.uuid4())[:8]
    return f"{prefix}_{unique_id}{suffix}"


async def copy_file(source: str, destination: str) -> bool:
    """Copy file from source to destination."""
    try:
        async with aiofiles.open(source, 'rb') as src:
            async with aiofiles.open(destination, 'wb') as dst:
                while chunk := await src.read(8192):
                    await dst.write(chunk)
        return True
    except Exception as e:
        logger.error("Failed to copy file", source=source, destination=destination, error=str(e))
        return False


def format_file_size(size_bytes: int) -> str:
    """Format file size in human-readable format."""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"
