# Excel Web Application

A modular, optimized web application for Excel file processing using Python, LibreOffice, and Docker.

## 🚀 Features

- 📁 **Upload Excel files** (.xls, .xlsx) up to 100MB
- 👀 **Preview files** in browser with Excel-like interface
- ✏️ **Edit cell values and formulas** with real-time updates
- 🔍 **Extract and display** original formulas with syntax highlighting
- 🖼️ **Display and manage** in-cell and floating images
- 📊 **Navigate between multiple sheets** with tab interface
- 📤 **Upload and insert new images** with drag-and-drop
- ⚡ **High performance** with Redis caching and async processing
- 🐳 **Docker containerized** for easy deployment
- 🔒 **Secure** with input validation and sanitization

## 🛠️ Tech Stack

### Backend
- **Python 3.11** with FastAPI for high-performance APIs
- **LibreOffice** (headless mode) for Excel processing
- **PyUNO API** for full control over sheets, cells, images, and formulas
- **Redis** for caching and performance optimization
- **SQLite/PostgreSQL** for metadata storage
- **Docker** for containerization and deployment

### Frontend
- **React 18** with TypeScript for type safety
- **AG Grid** for Excel-like data grid functionality
- **React Query** for efficient data fetching and caching
- **React Router** for navigation
- **Styled Components** for component styling
- **React Dropzone** for file uploads

### Infrastructure
- **Docker Compose** for multi-container orchestration
- **Nginx** for production reverse proxy
- **Redis** for caching and session management
- **Gunicorn** for production WSGI server

## 📁 Project Structure

```
excel-web-app/
├── backend/                    # Python FastAPI backend
│   ├── app/
│   │   ├── api/               # API endpoints
│   │   │   └── endpoints/     # Individual endpoint modules
│   │   ├── core/              # Core configuration and utilities
│   │   ├── models/            # Pydantic data models
│   │   ├── services/          # Business logic services
│   │   └── utils/             # Utility functions
│   ├── tests/                 # Comprehensive test suite
│   │   ├── unit/              # Unit tests
│   │   ├── integration/       # Integration tests
│   │   └── fixtures/          # Test fixtures
│   ├── requirements.txt       # Python dependencies
│   ├── Dockerfile             # Development Docker image
│   └── Dockerfile.prod        # Production Docker image
├── frontend/                   # React TypeScript frontend
│   ├── src/
│   │   ├── components/        # Reusable React components
│   │   ├── pages/             # Page components
│   │   ├── services/          # API service layer
│   │   ├── hooks/             # Custom React hooks
│   │   └── utils/             # Utility functions
│   ├── public/                # Static assets
│   ├── package.json           # Node.js dependencies
│   ├── Dockerfile             # Development Docker image
│   └── Dockerfile.prod        # Production Docker image
├── docker-compose.yml         # Development environment
├── docker-compose.prod.yml    # Production environment
├── setup.sh                   # Automated setup script
├── Makefile                   # Development commands
├── .env.example               # Environment variables template
└── README.md                  # This file
```

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose installed
- At least 4GB RAM available
- 2GB free disk space

### Option 1: Automated Setup (Recommended)
```bash
# Clone the repository
git clone <repository-url>
cd excel-web-app

# Run automated setup
./setup.sh

# Or use make command
make setup
```

### Option 2: Manual Setup
```bash
# Clone and navigate
git clone <repository-url>
cd excel-web-app

# Copy environment file
cp .env.example .env

# Create necessary directories
mkdir -p uploads temp logs

# Build and start services
docker-compose up --build
```

### Option 3: Development Mode
```bash
# Start in development mode with hot reload
make dev

# Or directly with docker-compose
docker-compose up --build
```

## 🔧 Development

### Available Commands
```bash
# Setup and initialization
make setup          # Initial setup
make dev             # Start development environment
make prod            # Start production environment

# Operations
make stop            # Stop all services
make restart         # Restart services
make clean           # Clean up containers and volumes
make logs            # View logs from all services
make status          # Show service status

# Development tools
make test            # Run all tests
make lint            # Run linting
make format          # Format code
make shell-backend   # Access backend shell
make shell-frontend  # Access frontend shell

# Monitoring
make monitor         # Monitor performance
make health          # Check application health
```

### Environment Configuration
Copy `.env.example` to `.env` and customize:

```bash
# Application
APP_NAME=Excel Web Application
DEBUG=true
ENVIRONMENT=development

# Server
HOST=0.0.0.0
PORT=8000

# Redis
REDIS_HOST=redis
REDIS_PORT=6379

# File Storage
UPLOAD_DIR=./uploads
TEMP_DIR=./temp
MAX_FILE_SIZE=100MB

# LibreOffice
LIBREOFFICE_PATH=/usr/bin/libreoffice
LIBREOFFICE_TIMEOUT=30

# Performance
MAX_CONCURRENT_CONVERSIONS=5
CACHE_TTL=3600
```

## 📚 API Documentation

Once running, access the interactive API documentation:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

### Key Endpoints

#### File Management
- `POST /api/v1/files/upload` - Upload Excel file
- `GET /api/v1/files/list` - List uploaded files
- `GET /api/v1/files/status/{file_id}` - Get processing status
- `DELETE /api/v1/files/{file_id}` - Delete file

#### Excel Operations
- `GET /api/v1/excel/{file_id}/preview` - Get Excel preview
- `GET /api/v1/excel/{file_id}/sheets` - Get sheet information
- `PUT /api/v1/excel/{file_id}/cells` - Update cell value/formula
- `GET /api/v1/excel/{file_id}/formulas` - Extract formulas

#### Image Management
- `GET /api/v1/images/{file_id}/images` - Get images
- `POST /api/v1/images/{file_id}/images` - Upload image
- `DELETE /api/v1/images/{file_id}/images/{image_id}` - Delete image

## 🌐 Application Access

After starting the application:

- **Frontend Application**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Redis**: localhost:6379

## ⚡ Performance Features

- **Async Processing**: Non-blocking file processing with background tasks
- **Redis Caching**: Intelligent caching of conversion results and metadata
- **Connection Pooling**: Optimized database and external service connections
- **Memory Management**: Automatic cleanup and garbage collection
- **Rate Limiting**: Prevents system overload with concurrent request limits
- **Resource Monitoring**: Real-time system resource monitoring
- **Optimized Docker Images**: Multi-stage builds for minimal image sizes

## 🧪 Testing

### Running Tests
```bash
# Run all tests
make test

# Run specific test types
make test-backend    # Backend tests only
make test-frontend   # Frontend tests only

# Run with coverage
docker-compose exec backend pytest --cov=app --cov-report=html

# Run specific test file
docker-compose exec backend pytest tests/unit/test_file_service.py -v
```

### Test Structure
- **Unit Tests**: Test individual functions and classes
- **Integration Tests**: Test API endpoints and service interactions
- **Fixtures**: Reusable test data and mock objects
- **Coverage**: Minimum 80% code coverage required

## 🚀 Production Deployment

### Production Setup
```bash
# Start production environment
make prod

# Or manually
docker-compose -f docker-compose.prod.yml up --build -d
```

### Production Features
- **Gunicorn** WSGI server with multiple workers
- **Nginx** reverse proxy with SSL termination
- **Optimized Docker images** with multi-stage builds
- **Health checks** and automatic restarts
- **Resource limits** and monitoring
- **Security headers** and CORS configuration

### Environment Variables for Production
```bash
DEBUG=false
ENVIRONMENT=production
LOG_LEVEL=INFO
WORKERS=4
MAX_CONCURRENT_CONVERSIONS=10
REDIS_PASSWORD=your-secure-password
SECRET_KEY=your-secret-key
```

## 🔒 Security

- **Input Validation**: Comprehensive validation of all inputs
- **File Type Validation**: Strict file type and size checking
- **CORS Configuration**: Proper cross-origin resource sharing setup
- **Security Headers**: X-Frame-Options, CSP, and other security headers
- **Non-root Containers**: All containers run as non-root users
- **Secret Management**: Environment-based secret configuration

## 📊 Monitoring and Logging

### Health Checks
```bash
# Check application health
curl http://localhost:8000/health

# Detailed health information
make health
```

### Logging
- **Structured Logging**: JSON-formatted logs for easy parsing
- **Log Levels**: Configurable log levels (DEBUG, INFO, WARNING, ERROR)
- **Log Rotation**: Automatic log rotation and cleanup
- **Centralized Logging**: All services log to centralized location

### Performance Monitoring
```bash
# Monitor resource usage
make monitor

# View performance metrics
curl http://localhost:8000/metrics
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow PEP 8 for Python code
- Use TypeScript for frontend development
- Write tests for new features
- Update documentation as needed
- Ensure all tests pass before submitting PR

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check this README and API docs
- **Issues**: Report bugs and feature requests via GitHub Issues
- **Discussions**: Join community discussions for questions and ideas

## 🙏 Acknowledgments

- **LibreOffice** for excellent document processing capabilities
- **FastAPI** for the high-performance web framework
- **React** and **AG Grid** for the modern frontend experience
- **Docker** for containerization and deployment simplification
