# Excel Web Application

A modular, optimized web application for Excel file processing using Python, LibreOffice, and Docker.

## Features

- 📁 Upload Excel files (.xls, .xlsx)
- 👀 Preview files in browser with Excel-like interface
- ✏️ Edit cell values and formulas
- 🔍 Extract and display original formulas
- 🖼️ Display and manage in-cell and floating images
- 📊 Navigate between multiple sheets
- 📤 Upload and insert new images

## Tech Stack

### Backend
- **Python 3.10+** with FastAPI
- **LibreOffice** (headless mode) for Excel processing
- **PyUNO API** for full control over sheets, cells, images, and formulas
- **Redis** for caching and performance optimization
- **Docker** for containerization

### Frontend
- **Modern Excel-like grid libraries** (Handsontable, AG Grid, or Luckysheet)
- **React/Vue.js** for UI components
- **WebSocket** for real-time updates

## Project Structure

```
excel-web-app/
├── backend/
│   ├── app/
│   │   ├── api/
│   │   ├── core/
│   │   ├── models/
│   │   ├── services/
│   │   └── utils/
│   ├── tests/
│   ├── requirements.txt
│   └── Dockerfile
├── frontend/
│   ├── src/
│   ├── public/
│   ├── package.json
│   └── Dockerfile
├── docker-compose.yml
├── .env.example
└── README.md
```

## Quick Start

1. Clone the repository
2. Copy `.env.example` to `.env` and configure
3. Run with Docker Compose:
   ```bash
   docker-compose up --build
   ```

## Development

See individual README files in `backend/` and `frontend/` directories for detailed setup instructions.

## Performance Features

- ⚡ Async endpoints for long-running operations
- 🗄️ Redis caching to avoid redundant conversions
- 🧹 Automatic cleanup of temporary files
- 🐳 Optimized Docker images using Alpine Linux
- 📦 Modular architecture for easy scaling

## API Documentation

Once running, visit:
- API Documentation: http://localhost:8000/docs
- Application: http://localhost:3000

## License

MIT License
