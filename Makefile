# Excel Web Application Makefile
# Provides convenient commands for development and deployment

.PHONY: help setup dev prod stop clean logs status test lint format install-deps

# Default target
help:
	@echo "Excel Web Application - Available Commands:"
	@echo ""
	@echo "Setup & Development:"
	@echo "  make setup          - Initial setup (create dirs, copy env, build)"
	@echo "  make dev            - Start development environment"
	@echo "  make prod           - Start production environment"
	@echo "  make install-deps   - Install development dependencies"
	@echo ""
	@echo "Operations:"
	@echo "  make stop           - Stop all services"
	@echo "  make restart        - Restart all services"
	@echo "  make clean          - Clean up containers and volumes"
	@echo "  make logs           - View logs from all services"
	@echo "  make status         - Show service status"
	@echo ""
	@echo "Development:"
	@echo "  make test           - Run tests"
	@echo "  make lint           - Run linting"
	@echo "  make format         - Format code"
	@echo ""
	@echo "Maintenance:"
	@echo "  make backup         - Backup data"
	@echo "  make update         - Update dependencies"
	@echo "  make health         - Check application health"

# Setup and initialization
setup:
	@echo "🚀 Setting up Excel Web Application..."
	@./setup.sh

# Development environment
dev:
	@echo "🔧 Starting development environment..."
	@docker-compose up --build

dev-detached:
	@echo "🔧 Starting development environment (detached)..."
	@docker-compose up --build -d

# Production environment
prod:
	@echo "🚀 Starting production environment..."
	@docker-compose -f docker-compose.prod.yml up --build -d

# Install development dependencies
install-deps:
	@echo "📦 Installing development dependencies..."
	@cd backend && pip install -r requirements.txt
	@cd frontend && npm install

# Operations
stop:
	@echo "🛑 Stopping services..."
	@docker-compose down

restart:
	@echo "🔄 Restarting services..."
	@docker-compose restart

clean:
	@echo "🧹 Cleaning up..."
	@docker-compose down -v --rmi all
	@docker system prune -f

# Monitoring
logs:
	@docker-compose logs -f

logs-backend:
	@docker-compose logs -f backend

logs-frontend:
	@docker-compose logs -f frontend

logs-redis:
	@docker-compose logs -f redis

status:
	@docker-compose ps

# Development tools
test:
	@echo "🧪 Running tests..."
	@docker-compose exec backend python -m pytest tests/ -v
	@cd frontend && npm test

test-backend:
	@echo "🧪 Running backend tests..."
	@docker-compose exec backend python -m pytest tests/ -v

test-frontend:
	@echo "🧪 Running frontend tests..."
	@cd frontend && npm test

lint:
	@echo "🔍 Running linting..."
	@docker-compose exec backend python -m flake8 app/
	@docker-compose exec backend python -m mypy app/
	@cd frontend && npm run lint

lint-backend:
	@echo "🔍 Running backend linting..."
	@docker-compose exec backend python -m flake8 app/
	@docker-compose exec backend python -m mypy app/

lint-frontend:
	@echo "🔍 Running frontend linting..."
	@cd frontend && npm run lint

format:
	@echo "✨ Formatting code..."
	@docker-compose exec backend python -m black app/
	@docker-compose exec backend python -m isort app/
	@cd frontend && npm run format

format-backend:
	@echo "✨ Formatting backend code..."
	@docker-compose exec backend python -m black app/
	@docker-compose exec backend python -m isort app/

format-frontend:
	@echo "✨ Formatting frontend code..."
	@cd frontend && npm run format

# Database operations
db-migrate:
	@echo "🗄️ Running database migrations..."
	@docker-compose exec backend alembic upgrade head

db-reset:
	@echo "🗄️ Resetting database..."
	@docker-compose exec backend python -c "from app.core.database import reset_database; reset_database()"

# Backup and maintenance
backup:
	@echo "💾 Creating backup..."
	@mkdir -p backups
	@docker-compose exec backend tar -czf /tmp/backup.tar.gz uploads/
	@docker cp $$(docker-compose ps -q backend):/tmp/backup.tar.gz backups/backup-$$(date +%Y%m%d-%H%M%S).tar.gz
	@echo "Backup created in backups/ directory"

update:
	@echo "📦 Updating dependencies..."
	@docker-compose pull
	@cd backend && pip install --upgrade -r requirements.txt
	@cd frontend && npm update

health:
	@echo "🏥 Checking application health..."
	@curl -f http://localhost:8000/health || echo "Backend health check failed"
	@curl -f http://localhost:3000 || echo "Frontend health check failed"

# Docker operations
build:
	@echo "🔨 Building images..."
	@docker-compose build

build-no-cache:
	@echo "🔨 Building images (no cache)..."
	@docker-compose build --no-cache

pull:
	@echo "📥 Pulling latest images..."
	@docker-compose pull

# Shell access
shell-backend:
	@docker-compose exec backend /bin/bash

shell-frontend:
	@docker-compose exec frontend /bin/sh

shell-redis:
	@docker-compose exec redis redis-cli

# Performance monitoring
monitor:
	@echo "📊 Monitoring performance..."
	@docker stats

# Security scanning
security-scan:
	@echo "🔒 Running security scan..."
	@docker run --rm -v $$(pwd):/app -w /app securecodewarrior/docker-security-scan

# Documentation
docs:
	@echo "📚 Opening documentation..."
	@open http://localhost:8000/docs

# Quick commands
quick-start: setup dev-detached status
	@echo "✅ Quick start completed!"

quick-stop: stop clean
	@echo "✅ Quick stop completed!"

# Environment specific commands
env-dev:
	@cp .env.example .env
	@echo "DEBUG=true" >> .env
	@echo "ENVIRONMENT=development" >> .env

env-prod:
	@cp .env.example .env
	@echo "DEBUG=false" >> .env
	@echo "ENVIRONMENT=production" >> .env
