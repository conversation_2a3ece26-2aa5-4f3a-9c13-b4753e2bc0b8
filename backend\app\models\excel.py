"""
Excel-related data models.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field
from enum import Enum


class FileStatus(str, Enum):
    """File processing status."""
    UPLOADING = "uploading"
    PROCESSING = "processing"
    READY = "ready"
    ERROR = "error"


class CellType(str, Enum):
    """Cell data types."""
    TEXT = "text"
    NUMBER = "number"
    FORMULA = "formula"
    DATE = "date"
    BOOLEAN = "boolean"
    ERROR = "error"
    EMPTY = "empty"


class ImageType(str, Enum):
    """Image types in Excel."""
    IN_CELL = "in_cell"
    FLOATING = "floating"
    BACKGROUND = "background"


class CellData(BaseModel):
    """Individual cell data."""
    row: int = Field(..., description="Row index (0-based)")
    col: int = Field(..., description="Column index (0-based)")
    value: Optional[Union[str, int, float, bool]] = Field(None, description="Cell value")
    formula: Optional[str] = Field(None, description="Cell formula if present")
    cell_type: CellType = Field(CellType.EMPTY, description="Cell data type")
    format: Optional[str] = Field(None, description="Cell format string")
    style: Optional[Dict[str, Any]] = Field(None, description="Cell styling information")


class ImageData(BaseModel):
    """Image data in Excel."""
    id: str = Field(..., description="Unique image identifier")
    name: str = Field(..., description="Image name")
    image_type: ImageType = Field(..., description="Type of image")
    position: Dict[str, Union[int, float]] = Field(..., description="Image position")
    size: Dict[str, Union[int, float]] = Field(..., description="Image dimensions")
    data: Optional[str] = Field(None, description="Base64 encoded image data")
    url: Optional[str] = Field(None, description="Image URL if stored separately")
    sheet_index: int = Field(..., description="Sheet index where image is located")


class SheetData(BaseModel):
    """Excel sheet data."""
    index: int = Field(..., description="Sheet index")
    name: str = Field(..., description="Sheet name")
    cells: List[CellData] = Field(default_factory=list, description="Sheet cells")
    images: List[ImageData] = Field(default_factory=list, description="Sheet images")
    row_count: int = Field(0, description="Number of rows with data")
    col_count: int = Field(0, description="Number of columns with data")
    hidden: bool = Field(False, description="Whether sheet is hidden")


class ExcelFileMetadata(BaseModel):
    """Excel file metadata."""
    file_id: str = Field(..., description="Unique file identifier")
    filename: str = Field(..., description="Original filename")
    file_size: int = Field(..., description="File size in bytes")
    file_hash: str = Field(..., description="File content hash")
    mime_type: str = Field(..., description="File MIME type")
    upload_time: datetime = Field(default_factory=datetime.utcnow, description="Upload timestamp")
    status: FileStatus = Field(FileStatus.UPLOADING, description="Processing status")
    error_message: Optional[str] = Field(None, description="Error message if processing failed")
    sheets: List[SheetData] = Field(default_factory=list, description="Excel sheets")
    total_sheets: int = Field(0, description="Total number of sheets")
    has_formulas: bool = Field(False, description="Whether file contains formulas")
    has_images: bool = Field(False, description="Whether file contains images")


class CellUpdateRequest(BaseModel):
    """Request to update a cell."""
    sheet_index: int = Field(..., description="Sheet index")
    row: int = Field(..., description="Row index (0-based)")
    col: int = Field(..., description="Column index (0-based)")
    value: Optional[Union[str, int, float, bool]] = Field(None, description="New cell value")
    formula: Optional[str] = Field(None, description="New cell formula")


class ImageUploadRequest(BaseModel):
    """Request to upload an image."""
    sheet_index: int = Field(..., description="Target sheet index")
    position: Dict[str, Union[int, float]] = Field(..., description="Image position")
    size: Optional[Dict[str, Union[int, float]]] = Field(None, description="Image size")
    image_type: ImageType = Field(ImageType.FLOATING, description="Image type")


class ExcelPreviewResponse(BaseModel):
    """Excel file preview response."""
    file_id: str = Field(..., description="File identifier")
    metadata: ExcelFileMetadata = Field(..., description="File metadata")
    html_preview: Optional[str] = Field(None, description="HTML preview of the file")
    current_sheet: int = Field(0, description="Currently selected sheet index")


class ProcessingStatus(BaseModel):
    """File processing status response."""
    file_id: str = Field(..., description="File identifier")
    status: FileStatus = Field(..., description="Current status")
    progress: float = Field(0.0, description="Processing progress (0-100)")
    message: str = Field("", description="Status message")
    error: Optional[str] = Field(None, description="Error message if failed")
