body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

* {
  box-sizing: border-box;
}

#root {
  min-height: 100vh;
}

/* Global styles for Excel-like interface */
.excel-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: white;
}

.excel-header {
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.excel-toolbar {
  background: #ffffff;
  border-bottom: 1px solid #dee2e6;
  padding: 0.5rem 1rem;
  display: flex;
  gap: 1rem;
  align-items: center;
}

.excel-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.excel-sidebar {
  width: 250px;
  background: #f8f9fa;
  border-right: 1px solid #dee2e6;
  padding: 1rem;
  overflow-y: auto;
}

.excel-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.excel-sheets {
  background: #f8f9fa;
  border-top: 1px solid #dee2e6;
  padding: 0.5rem 1rem;
  display: flex;
  gap: 0.5rem;
  overflow-x: auto;
}

.sheet-tab {
  padding: 0.5rem 1rem;
  background: white;
  border: 1px solid #dee2e6;
  border-bottom: none;
  cursor: pointer;
  white-space: nowrap;
  border-radius: 4px 4px 0 0;
}

.sheet-tab.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.sheet-tab:hover:not(.active) {
  background: #e9ecef;
}

/* Loading and error states */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.1rem;
  color: #6c757d;
}

.error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.1rem;
  color: #dc3545;
  text-align: center;
}

/* Button styles */
.btn {
  padding: 0.5rem 1rem;
  border: 1px solid #dee2e6;
  background: white;
  cursor: pointer;
  border-radius: 4px;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.btn:hover {
  background: #f8f9fa;
}

.btn-primary {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.btn-primary:hover {
  background: #0056b3;
  border-color: #0056b3;
}

.btn-danger {
  background: #dc3545;
  color: white;
  border-color: #dc3545;
}

.btn-danger:hover {
  background: #c82333;
  border-color: #c82333;
}

/* Form styles */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 0.9rem;
}

.form-control:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Responsive design */
@media (max-width: 768px) {
  .excel-content {
    flex-direction: column;
  }
  
  .excel-sidebar {
    width: 100%;
    height: auto;
    max-height: 200px;
  }
  
  .excel-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
}
