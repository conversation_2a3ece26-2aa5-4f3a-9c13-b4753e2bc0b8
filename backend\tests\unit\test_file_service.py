"""
Unit tests for file service.
"""

import pytest
from pathlib import Path
from unittest.mock import AsyncMock, patch

from app.services.file_service import file_service
from app.models.excel import ExcelFileMetadata, FileStatus


class TestFileService:
    """Test cases for FileService."""
    
    @pytest.mark.asyncio
    async def test_save_uploaded_file(self, setup_test_environment, sample_upload_file, temp_dir):
        """Test saving uploaded file."""
        file_id = "test-file-123"
        
        # Reset file pointer
        sample_upload_file.file.seek(0)
        
        file_path = await file_service.save_uploaded_file(sample_upload_file, file_id)
        
        assert file_path is not None
        assert Path(file_path).exists()
        assert Path(file_path).name == f"{file_id}.xlsx"
    
    @pytest.mark.asyncio
    async def test_save_and_get_metadata(self, setup_test_environment, sample_file_metadata):
        """Test saving and retrieving metadata."""
        file_id = sample_file_metadata.file_id
        
        # Save metadata
        success = await file_service.save_metadata(file_id, sample_file_metadata)
        assert success is True
        
        # Retrieve metadata
        retrieved_metadata = await file_service.get_metadata(file_id)
        assert retrieved_metadata is not None
        assert retrieved_metadata.file_id == file_id
        assert retrieved_metadata.filename == sample_file_metadata.filename
    
    @pytest.mark.asyncio
    async def test_update_metadata(self, setup_test_environment, sample_file_metadata):
        """Test updating metadata."""
        file_id = sample_file_metadata.file_id
        
        # Save initial metadata
        await file_service.save_metadata(file_id, sample_file_metadata)
        
        # Update metadata
        updates = {"status": FileStatus.PROCESSING}
        success = await file_service.update_metadata(file_id, updates)
        assert success is True
        
        # Verify update
        updated_metadata = await file_service.get_metadata(file_id)
        assert updated_metadata.status == FileStatus.PROCESSING
    
    @pytest.mark.asyncio
    async def test_list_files(self, setup_test_environment, sample_file_metadata):
        """Test listing files."""
        # Save some test metadata
        await file_service.save_metadata("file1", sample_file_metadata)
        
        metadata2 = sample_file_metadata.model_copy()
        metadata2.file_id = "file2"
        metadata2.filename = "test2.xlsx"
        await file_service.save_metadata("file2", metadata2)
        
        # List files
        files = await file_service.list_files()
        assert len(files) >= 2
        
        file_ids = [f.file_id for f in files]
        assert "file1" in file_ids
        assert "file2" in file_ids
    
    @pytest.mark.asyncio
    async def test_delete_file(self, setup_test_environment, sample_file_metadata, sample_upload_file):
        """Test deleting file and metadata."""
        file_id = sample_file_metadata.file_id
        
        # Save file and metadata
        sample_upload_file.file.seek(0)
        file_path = await file_service.save_uploaded_file(sample_upload_file, file_id)
        await file_service.save_metadata(file_id, sample_file_metadata)
        
        # Verify file exists
        assert Path(file_path).exists()
        assert await file_service.get_metadata(file_id) is not None
        
        # Delete file
        success = await file_service.delete_file(file_id)
        assert success is True
        
        # Verify file is deleted
        assert not Path(file_path).exists()
        assert await file_service.get_metadata(file_id) is None
    
    @pytest.mark.asyncio
    async def test_get_file_path(self, setup_test_environment, sample_file_metadata, sample_upload_file):
        """Test getting file path."""
        file_id = sample_file_metadata.file_id
        
        # Save file and metadata
        sample_upload_file.file.seek(0)
        saved_path = await file_service.save_uploaded_file(sample_upload_file, file_id)
        await file_service.save_metadata(file_id, sample_file_metadata)
        
        # Get file path
        retrieved_path = await file_service.get_file_path(file_id)
        assert retrieved_path == saved_path
        assert Path(retrieved_path).exists()
    
    @pytest.mark.asyncio
    async def test_get_nonexistent_metadata(self, setup_test_environment):
        """Test getting metadata for non-existent file."""
        metadata = await file_service.get_metadata("nonexistent-file")
        assert metadata is None
    
    @pytest.mark.asyncio
    async def test_delete_nonexistent_file(self, setup_test_environment):
        """Test deleting non-existent file."""
        success = await file_service.delete_file("nonexistent-file")
        assert success is False
    
    @pytest.mark.asyncio
    async def test_cleanup_temp_files(self, setup_test_environment, temp_dir):
        """Test cleaning up temporary files."""
        # Create some temporary files
        temp_file1 = Path(file_service.temp_dir) / "temp1.txt"
        temp_file2 = Path(file_service.temp_dir) / "temp2.txt"
        
        temp_file1.write_text("temp content 1")
        temp_file2.write_text("temp content 2")
        
        assert temp_file1.exists()
        assert temp_file2.exists()
        
        # Clean up with max_age of 0 (should delete all files)
        cleaned_count = await file_service.cleanup_temp_files(max_age_seconds=0)
        
        assert cleaned_count >= 2
        assert not temp_file1.exists()
        assert not temp_file2.exists()
