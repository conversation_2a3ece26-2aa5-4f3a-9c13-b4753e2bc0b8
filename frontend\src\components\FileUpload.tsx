import React, { useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { FiUpload, FiFile } from 'react-icons/fi';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { fileApi } from '../services/api';

interface FileUploadProps {
  onUploadSuccess?: (fileId: string) => void;
}

const FileUpload: React.FC<FileUploadProps> = ({ onUploadSuccess }) => {
  const queryClient = useQueryClient();

  const uploadMutation = useMutation({
    mutationFn: fileApi.uploadFile,
    onSuccess: (data) => {
      toast.success('File uploaded successfully!');
      queryClient.invalidateQueries({ queryKey: ['files'] });
      onUploadSuccess?.(data.file_id);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || 'Upload failed');
    },
  });

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      
      // Validate file type
      const allowedTypes = [
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      ];
      
      if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xls|xlsx)$/i)) {
        toast.error('Please upload a valid Excel file (.xls or .xlsx)');
        return;
      }

      // Validate file size (100MB limit)
      if (file.size > 100 * 1024 * 1024) {
        toast.error('File size must be less than 100MB');
        return;
      }

      uploadMutation.mutate(file);
    }
  }, [uploadMutation]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
    },
    multiple: false,
  });

  return (
    <div
      {...getRootProps()}
      style={{
        border: '2px dashed #007bff',
        borderRadius: '8px',
        padding: '2rem',
        textAlign: 'center',
        cursor: 'pointer',
        backgroundColor: isDragActive ? '#f8f9fa' : 'white',
        transition: 'all 0.2s ease',
        minHeight: '200px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        gap: '1rem',
      }}
    >
      <input {...getInputProps()} />
      
      {uploadMutation.isPending ? (
        <>
          <div style={{ fontSize: '2rem' }}>⏳</div>
          <div>Uploading...</div>
        </>
      ) : (
        <>
          <FiUpload size={48} color="#007bff" />
          
          {isDragActive ? (
            <div>
              <h3>Drop the Excel file here</h3>
              <p>Release to upload</p>
            </div>
          ) : (
            <div>
              <h3>Upload Excel File</h3>
              <p>Drag and drop an Excel file here, or click to select</p>
              <p style={{ fontSize: '0.9rem', color: '#6c757d' }}>
                Supports .xls and .xlsx files up to 100MB
              </p>
            </div>
          )}
          
          <button 
            type="button" 
            className="btn btn-primary"
            style={{ marginTop: '1rem' }}
          >
            <FiFile style={{ marginRight: '0.5rem' }} />
            Choose File
          </button>
        </>
      )}
    </div>
  );
};

export default FileUpload;
