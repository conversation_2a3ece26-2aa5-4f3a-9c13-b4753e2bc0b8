"""
Image management endpoints for Excel files.
"""

from typing import List
from fastapi import APIRouter, File, HTTPException, UploadFile, Form
from fastapi.responses import StreamingResponse
import structlog

from app.models.excel import ImageData, ImageUploadRequest, ImageType
from app.services.image_service import image_service
from app.services.file_service import file_service

logger = structlog.get_logger(__name__)
router = APIRouter()


@router.get("/{file_id}/images", response_model=List[ImageData])
async def get_images(file_id: str, sheet_index: int = None):
    """
    Get all images from a file or specific sheet.
    """
    try:
        # Validate file exists
        metadata = await file_service.get_metadata(file_id)
        if not metadata:
            raise HTTPException(status_code=404, detail="File not found")
        
        images = await image_service.get_images(file_id, sheet_index)
        return images
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get images", file_id=file_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get images")


@router.get("/{file_id}/images/{image_id}")
async def get_image(file_id: str, image_id: str):
    """
    Get a specific image by ID.
    """
    try:
        image_stream = await image_service.get_image_stream(file_id, image_id)
        if not image_stream:
            raise HTTPException(status_code=404, detail="Image not found")
        
        return StreamingResponse(
            image_stream.stream,
            media_type=image_stream.content_type,
            headers={"Content-Disposition": f"inline; filename={image_stream.filename}"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get image", file_id=file_id, image_id=image_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get image")


@router.post("/{file_id}/images", response_model=ImageData)
async def upload_image(
    file_id: str,
    image: UploadFile = File(...),
    sheet_index: int = Form(...),
    position_x: float = Form(...),
    position_y: float = Form(...),
    width: float = Form(None),
    height: float = Form(None),
    image_type: ImageType = Form(ImageType.FLOATING)
):
    """
    Upload and insert a new image into the Excel file.
    """
    try:
        # Validate file exists and is ready
        metadata = await file_service.get_metadata(file_id)
        if not metadata:
            raise HTTPException(status_code=404, detail="File not found")
        
        if metadata.status != "ready":
            raise HTTPException(
                status_code=400, 
                detail=f"File is not ready for editing. Status: {metadata.status}"
            )
        
        # Validate image file
        if not image.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # Prepare upload request
        upload_request = ImageUploadRequest(
            sheet_index=sheet_index,
            position={"x": position_x, "y": position_y},
            size={"width": width, "height": height} if width and height else None,
            image_type=image_type
        )
        
        # Upload and insert image
        image_data = await image_service.upload_image(file_id, image, upload_request)
        
        logger.info(
            "Image uploaded successfully", 
            file_id=file_id, 
            image_id=image_data.id,
            sheet_index=sheet_index
        )
        
        return image_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to upload image", file_id=file_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to upload image")


@router.put("/{file_id}/images/{image_id}", response_model=ImageData)
async def update_image(
    file_id: str,
    image_id: str,
    position_x: float = Form(None),
    position_y: float = Form(None),
    width: float = Form(None),
    height: float = Form(None)
):
    """
    Update image position and size.
    """
    try:
        # Validate file exists
        metadata = await file_service.get_metadata(file_id)
        if not metadata:
            raise HTTPException(status_code=404, detail="File not found")
        
        # Prepare update data
        update_data = {}
        if position_x is not None and position_y is not None:
            update_data["position"] = {"x": position_x, "y": position_y}
        if width is not None and height is not None:
            update_data["size"] = {"width": width, "height": height}
        
        if not update_data:
            raise HTTPException(status_code=400, detail="No update data provided")
        
        # Update image
        updated_image = await image_service.update_image(file_id, image_id, update_data)
        if not updated_image:
            raise HTTPException(status_code=404, detail="Image not found")
        
        logger.info("Image updated successfully", file_id=file_id, image_id=image_id)
        
        return updated_image
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update image", file_id=file_id, image_id=image_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to update image")


@router.delete("/{file_id}/images/{image_id}")
async def delete_image(file_id: str, image_id: str):
    """
    Delete an image from the Excel file.
    """
    try:
        success = await image_service.delete_image(file_id, image_id)
        if not success:
            raise HTTPException(status_code=404, detail="Image not found")
        
        logger.info("Image deleted successfully", file_id=file_id, image_id=image_id)
        
        return {"message": "Image deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to delete image", file_id=file_id, image_id=image_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to delete image")
