{"name": "excel-web-app-frontend", "version": "1.0.0", "description": "Frontend for Excel Web Application", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^27.5.2", "@types/node": "^16.18.68", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "axios": "^1.6.2", "react-router-dom": "^6.20.1", "react-query": "^3.39.3", "@tanstack/react-query": "^5.8.4", "react-dropzone": "^14.2.3", "react-hot-toast": "^2.4.1", "luckysheet": "^2.1.13", "handsontable": "^13.1.0", "@handsontable/react": "^13.1.0", "ag-grid-react": "^31.0.3", "ag-grid-community": "^31.0.3", "styled-components": "^6.1.1", "@types/styled-components": "^5.1.34", "react-icons": "^4.12.0", "framer-motion": "^10.16.16"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,md}"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-router-dom": "^5.3.3", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0"}, "proxy": "http://localhost:8000"}