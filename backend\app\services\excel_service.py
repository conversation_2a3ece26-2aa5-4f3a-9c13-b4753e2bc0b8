"""
Excel processing service using LibreOffice and PyUNO.
"""

import asyncio
import os
import subprocess
import tempfile
import uuid
from pathlib import Path
from typing import Dict, List, Optional, Any
import structlog

from app.core.config import settings
from app.core.redis import redis_client
from app.models.excel import (
    ExcelFileMetadata, FileStatus, SheetData, CellData, ImageData,
    CellUpdateRequest, CellType, ImageType
)
from app.services.file_service import file_service
from app.utils.libreoffice_utils import LibreOfficeConverter

logger = structlog.get_logger(__name__)


class ExcelService:
    """Service for Excel file processing and manipulation."""
    
    def __init__(self):
        self.converter = LibreOfficeConverter()
        self.processing_semaphore = asyncio.Semaphore(settings.MAX_CONCURRENT_CONVERSIONS)
    
    async def process_excel_file(self, file_id: str, file_path: str) -> bool:
        """Process an Excel file and extract all data."""
        async with self.processing_semaphore:
            try:
                logger.info("Starting Excel file processing", file_id=file_id)
                
                # Update status to processing
                await file_service.update_metadata(file_id, {"status": FileStatus.PROCESSING})
                
                # Check cache first
                metadata = await file_service.get_metadata(file_id)
                if not metadata:
                    logger.error("File metadata not found", file_id=file_id)
                    return False
                
                cached_result = await redis_client.get_cached_conversion(metadata.file_hash)
                if cached_result:
                    logger.info("Using cached conversion result", file_id=file_id)
                    await self._apply_cached_result(file_id, cached_result)
                    return True
                
                # Process the file
                processing_result = await self._process_file_with_libreoffice(file_path)
                if not processing_result:
                    await file_service.update_metadata(file_id, {
                        "status": FileStatus.ERROR,
                        "error_message": "Failed to process Excel file"
                    })
                    return False
                
                # Extract data from processed file
                excel_data = await self._extract_excel_data(processing_result)
                
                # Update metadata with extracted data
                update_data = {
                    "status": FileStatus.READY,
                    "sheets": [sheet.model_dump() for sheet in excel_data["sheets"]],
                    "total_sheets": len(excel_data["sheets"]),
                    "has_formulas": excel_data["has_formulas"],
                    "has_images": excel_data["has_images"]
                }
                
                await file_service.update_metadata(file_id, update_data)
                
                # Cache the result
                await redis_client.cache_file_conversion(
                    metadata.file_hash,
                    {
                        "html_preview": processing_result.get("html_content"),
                        "excel_data": excel_data
                    }
                )
                
                logger.info("Excel file processing completed", file_id=file_id)
                return True
                
            except Exception as e:
                logger.error("Excel file processing failed", file_id=file_id, error=str(e))
                await file_service.update_metadata(file_id, {
                    "status": FileStatus.ERROR,
                    "error_message": str(e)
                })
                return False
    
    async def _process_file_with_libreoffice(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Process file using LibreOffice."""
        try:
            # Convert to HTML for preview
            html_output = await self.converter.convert_to_html(file_path)
            
            # Extract data using PyUNO
            excel_data = await self.converter.extract_data_with_uno(file_path)
            
            return {
                "html_content": html_output,
                "excel_data": excel_data
            }
            
        except Exception as e:
            logger.error("LibreOffice processing failed", file_path=file_path, error=str(e))
            return None
    
    async def _extract_excel_data(self, processing_result: Dict[str, Any]) -> Dict[str, Any]:
        """Extract structured data from processing result."""
        excel_data = processing_result.get("excel_data", {})
        
        sheets = []
        has_formulas = False
        has_images = False
        
        for sheet_info in excel_data.get("sheets", []):
            # Extract cells
            cells = []
            for cell_info in sheet_info.get("cells", []):
                cell = CellData(
                    row=cell_info["row"],
                    col=cell_info["col"],
                    value=cell_info.get("value"),
                    formula=cell_info.get("formula"),
                    cell_type=CellType(cell_info.get("cell_type", "empty")),
                    format=cell_info.get("format"),
                    style=cell_info.get("style")
                )
                cells.append(cell)
                
                if cell.formula:
                    has_formulas = True
            
            # Extract images
            images = []
            for image_info in sheet_info.get("images", []):
                image = ImageData(
                    id=image_info["id"],
                    name=image_info["name"],
                    image_type=ImageType(image_info["image_type"]),
                    position=image_info["position"],
                    size=image_info["size"],
                    data=image_info.get("data"),
                    url=image_info.get("url"),
                    sheet_index=sheet_info["index"]
                )
                images.append(image)
                has_images = True
            
            sheet = SheetData(
                index=sheet_info["index"],
                name=sheet_info["name"],
                cells=cells,
                images=images,
                row_count=sheet_info.get("row_count", 0),
                col_count=sheet_info.get("col_count", 0),
                hidden=sheet_info.get("hidden", False)
            )
            sheets.append(sheet)
        
        return {
            "sheets": sheets,
            "has_formulas": has_formulas,
            "has_images": has_images
        }
    
    async def _apply_cached_result(self, file_id: str, cached_result: Dict[str, Any]) -> None:
        """Apply cached conversion result to file metadata."""
        excel_data = cached_result.get("excel_data", {})
        
        update_data = {
            "status": FileStatus.READY,
            "sheets": [sheet.model_dump() for sheet in excel_data.get("sheets", [])],
            "total_sheets": len(excel_data.get("sheets", [])),
            "has_formulas": excel_data.get("has_formulas", False),
            "has_images": excel_data.get("has_images", False)
        }
        
        await file_service.update_metadata(file_id, update_data)
    
    async def get_html_preview(self, file_id: str, sheet_index: int = 0) -> Optional[str]:
        """Get HTML preview of Excel file."""
        try:
            metadata = await file_service.get_metadata(file_id)
            if not metadata:
                return None
            
            # Check cache first
            cached_result = await redis_client.get_cached_conversion(metadata.file_hash)
            if cached_result and cached_result.get("html_preview"):
                return cached_result["html_preview"]
            
            # Generate HTML preview if not cached
            file_path = await file_service.get_file_path(file_id)
            if not file_path:
                return None
            
            html_content = await self.converter.convert_to_html(file_path, sheet_index)
            
            # Cache the result
            await redis_client.cache_file_conversion(
                metadata.file_hash,
                {"html_preview": html_content}
            )
            
            return html_content
            
        except Exception as e:
            logger.error("Failed to get HTML preview", file_id=file_id, error=str(e))
            return None
    
    async def get_sheet_data(self, file_id: str, sheet_index: int) -> Optional[SheetData]:
        """Get detailed data for a specific sheet."""
        try:
            metadata = await file_service.get_metadata(file_id)
            if not metadata or sheet_index >= len(metadata.sheets):
                return None
            
            return SheetData(**metadata.sheets[sheet_index])
            
        except Exception as e:
            logger.error("Failed to get sheet data", file_id=file_id, sheet_index=sheet_index, error=str(e))
            return None
    
    async def update_cell(self, file_id: str, cell_update: CellUpdateRequest) -> Optional[CellData]:
        """Update a cell value or formula."""
        try:
            # This would require PyUNO integration to modify the actual file
            # For now, we'll update the cached data
            metadata = await file_service.get_metadata(file_id)
            if not metadata:
                return None
            
            # Find and update the cell in metadata
            if cell_update.sheet_index >= len(metadata.sheets):
                return None
            
            sheet = metadata.sheets[cell_update.sheet_index]
            
            # Find existing cell or create new one
            cell_found = False
            for i, cell_data in enumerate(sheet["cells"]):
                if cell_data["row"] == cell_update.row and cell_data["col"] == cell_update.col:
                    # Update existing cell
                    if cell_update.value is not None:
                        cell_data["value"] = cell_update.value
                    if cell_update.formula is not None:
                        cell_data["formula"] = cell_update.formula
                    cell_found = True
                    break
            
            if not cell_found:
                # Create new cell
                new_cell = {
                    "row": cell_update.row,
                    "col": cell_update.col,
                    "value": cell_update.value,
                    "formula": cell_update.formula,
                    "cell_type": "formula" if cell_update.formula else "text",
                    "format": None,
                    "style": None
                }
                sheet["cells"].append(new_cell)
            
            # Update metadata
            await file_service.update_metadata(file_id, {"sheets": metadata.sheets})
            
            # Return updated cell data
            return CellData(
                row=cell_update.row,
                col=cell_update.col,
                value=cell_update.value,
                formula=cell_update.formula,
                cell_type=CellType.FORMULA if cell_update.formula else CellType.TEXT
            )
            
        except Exception as e:
            logger.error("Failed to update cell", file_id=file_id, error=str(e))
            return None
    
    async def extract_formulas(self, file_id: str, sheet_index: Optional[int] = None) -> List[Dict[str, Any]]:
        """Extract all formulas from the file or specific sheet."""
        try:
            metadata = await file_service.get_metadata(file_id)
            if not metadata:
                return []
            
            formulas = []
            sheets_to_check = [metadata.sheets[sheet_index]] if sheet_index is not None else metadata.sheets
            
            for sheet in sheets_to_check:
                for cell_data in sheet["cells"]:
                    if cell_data.get("formula"):
                        formulas.append({
                            "sheet_index": sheet["index"],
                            "sheet_name": sheet["name"],
                            "row": cell_data["row"],
                            "col": cell_data["col"],
                            "formula": cell_data["formula"],
                            "value": cell_data.get("value")
                        })
            
            return formulas
            
        except Exception as e:
            logger.error("Failed to extract formulas", file_id=file_id, error=str(e))
            return []
    
    async def recalculate_formulas(self, file_id: str) -> bool:
        """Recalculate all formulas in the Excel file."""
        try:
            # This would require PyUNO integration to recalculate formulas
            # For now, we'll return success
            logger.info("Formula recalculation requested", file_id=file_id)
            return True
            
        except Exception as e:
            logger.error("Failed to recalculate formulas", file_id=file_id, error=str(e))
            return False


# Global Excel service instance
excel_service = ExcelService()
