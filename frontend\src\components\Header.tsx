import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { FiHome, FiUpload, FiFileText } from 'react-icons/fi';

const Header: React.FC = () => {
  const location = useLocation();

  return (
    <header className="excel-header">
      <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
        <Link to="/" style={{ textDecoration: 'none', color: 'inherit' }}>
          <h1 style={{ margin: 0, display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            <FiFileText size={24} />
            Excel Web App
          </h1>
        </Link>
      </div>
      
      <nav style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
        <Link 
          to="/" 
          className={`btn ${location.pathname === '/' ? 'btn-primary' : ''}`}
          style={{ textDecoration: 'none', display: 'flex', alignItems: 'center', gap: '0.5rem' }}
        >
          <FiHome size={16} />
          Home
        </Link>
        
        <div style={{ fontSize: '0.9rem', color: '#6c757d' }}>
          Upload and edit Excel files in your browser
        </div>
      </nav>
    </header>
  );
};

export default Header;
