#!/bin/bash

# Excel Web Application Setup Script
# This script sets up the development environment for the Excel Web Application

set -e  # Exit on any error

echo "🚀 Setting up Excel Web Application..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Docker and Docker Compose are installed"
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p uploads
    mkdir -p temp
    mkdir -p logs
    mkdir -p ssl
    
    print_success "Directories created"
}

# Copy environment file
setup_environment() {
    print_status "Setting up environment configuration..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        print_success "Environment file created from template"
        print_warning "Please review and update the .env file with your specific configuration"
    else
        print_warning ".env file already exists, skipping creation"
    fi
}

# Build and start services
start_services() {
    print_status "Building and starting services..."
    
    # Build images
    docker-compose build
    
    # Start services
    docker-compose up -d
    
    print_success "Services started successfully"
}

# Wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    # Wait for backend
    print_status "Waiting for backend service..."
    timeout=60
    counter=0
    
    while [ $counter -lt $timeout ]; do
        if curl -f http://localhost:8000/health &> /dev/null; then
            print_success "Backend service is ready"
            break
        fi
        
        sleep 2
        counter=$((counter + 2))
        
        if [ $counter -ge $timeout ]; then
            print_error "Backend service failed to start within $timeout seconds"
            exit 1
        fi
    done
    
    # Wait for frontend
    print_status "Waiting for frontend service..."
    counter=0
    
    while [ $counter -lt $timeout ]; do
        if curl -f http://localhost:3000 &> /dev/null; then
            print_success "Frontend service is ready"
            break
        fi
        
        sleep 2
        counter=$((counter + 2))
        
        if [ $counter -ge $timeout ]; then
            print_error "Frontend service failed to start within $timeout seconds"
            exit 1
        fi
    done
}

# Display service status
show_status() {
    print_status "Service Status:"
    docker-compose ps
    
    echo ""
    print_success "🎉 Excel Web Application is ready!"
    echo ""
    echo "📱 Frontend: http://localhost:3000"
    echo "🔧 Backend API: http://localhost:8000"
    echo "📚 API Documentation: http://localhost:8000/docs"
    echo "📊 Redis: localhost:6379"
    echo ""
    echo "📝 Logs:"
    echo "  - View all logs: docker-compose logs -f"
    echo "  - Backend logs: docker-compose logs -f backend"
    echo "  - Frontend logs: docker-compose logs -f frontend"
    echo ""
    echo "🛑 To stop: docker-compose down"
    echo "🔄 To restart: docker-compose restart"
    echo ""
}

# Main execution
main() {
    echo "=================================================="
    echo "    Excel Web Application Setup"
    echo "=================================================="
    echo ""
    
    check_docker
    create_directories
    setup_environment
    start_services
    wait_for_services
    show_status
}

# Handle script arguments
case "${1:-}" in
    "dev")
        print_status "Starting in development mode..."
        docker-compose up --build
        ;;
    "prod")
        print_status "Starting in production mode..."
        docker-compose -f docker-compose.prod.yml up --build -d
        ;;
    "stop")
        print_status "Stopping services..."
        docker-compose down
        print_success "Services stopped"
        ;;
    "clean")
        print_status "Cleaning up..."
        docker-compose down -v --rmi all
        docker system prune -f
        print_success "Cleanup completed"
        ;;
    "logs")
        docker-compose logs -f
        ;;
    "status")
        docker-compose ps
        ;;
    *)
        main
        ;;
esac
