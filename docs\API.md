# API Documentation

This document provides detailed information about the Excel Web Application API endpoints.

## Base URL

- Development: `http://localhost:8000`
- Production: `https://your-domain.com`

## Authentication

Currently, the API does not require authentication. In a production environment, you should implement proper authentication and authorization.

## Error Handling

All API endpoints return consistent error responses:

```json
{
  "detail": "Error message description",
  "status_code": 400,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Common HTTP Status Codes

- `200` - Success
- `400` - Bad Request (validation error)
- `404` - Not Found
- `422` - Unprocessable Entity (validation error)
- `500` - Internal Server Error

## File Management Endpoints

### Upload File

Upload an Excel file for processing.

**Endpoint:** `POST /api/v1/files/upload`

**Content-Type:** `multipart/form-data`

**Parameters:**
- `file` (required): Excel file (.xls or .xlsx)

**Response:**
```json
{
  "file_id": "uuid-string",
  "status": "processing",
  "progress": 0.0,
  "message": "File uploaded, processing started"
}
```

**Example:**
```bash
curl -X POST "http://localhost:8000/api/v1/files/upload" \
  -F "file=@example.xlsx"
```

### Get File Status

Get the processing status of an uploaded file.

**Endpoint:** `GET /api/v1/files/status/{file_id}`

**Parameters:**
- `file_id` (path): Unique file identifier

**Response:**
```json
{
  "file_id": "uuid-string",
  "status": "ready",
  "progress": 100.0,
  "message": "File is ready",
  "error": null
}
```

**Status Values:**
- `uploading` - File is being uploaded
- `processing` - File is being processed
- `ready` - File is ready for viewing/editing
- `error` - Processing failed

### List Files

Get a list of all uploaded files.

**Endpoint:** `GET /api/v1/files/list`

**Response:**
```json
[
  {
    "file_id": "uuid-string",
    "filename": "example.xlsx",
    "file_size": 1024000,
    "upload_time": "2024-01-01T12:00:00Z",
    "status": "ready",
    "total_sheets": 3,
    "has_formulas": true,
    "has_images": false
  }
]
```

### Delete File

Delete a file and all associated data.

**Endpoint:** `DELETE /api/v1/files/{file_id}`

**Parameters:**
- `file_id` (path): Unique file identifier

**Response:**
```json
{
  "message": "File deleted successfully"
}
```

### Get File Metadata

Get detailed metadata for a specific file.

**Endpoint:** `GET /api/v1/files/{file_id}/metadata`

**Response:**
```json
{
  "file_id": "uuid-string",
  "filename": "example.xlsx",
  "file_size": 1024000,
  "file_hash": "sha256-hash",
  "mime_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  "upload_time": "2024-01-01T12:00:00Z",
  "status": "ready",
  "sheets": [...],
  "total_sheets": 3,
  "has_formulas": true,
  "has_images": false
}
```

## Excel Operations Endpoints

### Get Excel Preview

Get a preview of the Excel file with HTML rendering.

**Endpoint:** `GET /api/v1/excel/{file_id}/preview`

**Parameters:**
- `file_id` (path): Unique file identifier
- `sheet_index` (query, optional): Sheet index to preview (default: 0)

**Response:**
```json
{
  "file_id": "uuid-string",
  "metadata": {...},
  "html_preview": "<html>...</html>",
  "current_sheet": 0
}
```

### Get HTML Preview

Get raw HTML preview of the Excel file.

**Endpoint:** `GET /api/v1/excel/{file_id}/preview/html`

**Parameters:**
- `file_id` (path): Unique file identifier
- `sheet_index` (query, optional): Sheet index to preview (default: 0)

**Response:** Raw HTML content

### Get Sheets

Get information about all sheets in the Excel file.

**Endpoint:** `GET /api/v1/excel/{file_id}/sheets`

**Response:**
```json
[
  {
    "index": 0,
    "name": "Sheet1",
    "cells": [...],
    "images": [...],
    "row_count": 100,
    "col_count": 10,
    "hidden": false
  }
]
```

### Get Sheet Data

Get detailed data for a specific sheet.

**Endpoint:** `GET /api/v1/excel/{file_id}/sheets/{sheet_index}`

**Parameters:**
- `file_id` (path): Unique file identifier
- `sheet_index` (path): Sheet index

**Response:**
```json
{
  "index": 0,
  "name": "Sheet1",
  "cells": [
    {
      "row": 0,
      "col": 0,
      "value": "Hello",
      "formula": null,
      "cell_type": "text",
      "format": null,
      "style": {...}
    }
  ],
  "images": [...],
  "row_count": 100,
  "col_count": 10,
  "hidden": false
}
```

### Update Cell

Update a cell value or formula.

**Endpoint:** `PUT /api/v1/excel/{file_id}/cells`

**Request Body:**
```json
{
  "sheet_index": 0,
  "row": 0,
  "col": 0,
  "value": "New Value",
  "formula": "=SUM(A1:A10)"
}
```

**Response:**
```json
{
  "row": 0,
  "col": 0,
  "value": "New Value",
  "formula": "=SUM(A1:A10)",
  "cell_type": "formula"
}
```

### Get Formulas

Extract all formulas from the file or a specific sheet.

**Endpoint:** `GET /api/v1/excel/{file_id}/formulas`

**Parameters:**
- `file_id` (path): Unique file identifier
- `sheet_index` (query, optional): Specific sheet index

**Response:**
```json
{
  "formulas": [
    {
      "sheet_index": 0,
      "sheet_name": "Sheet1",
      "row": 2,
      "col": 3,
      "formula": "=SUM(A1:A10)",
      "value": 55
    }
  ]
}
```

### Recalculate Formulas

Recalculate all formulas in the Excel file.

**Endpoint:** `POST /api/v1/excel/{file_id}/recalculate`

**Response:**
```json
{
  "message": "Formulas recalculated successfully"
}
```

## Image Management Endpoints

### Get Images

Get all images from a file or specific sheet.

**Endpoint:** `GET /api/v1/images/{file_id}/images`

**Parameters:**
- `file_id` (path): Unique file identifier
- `sheet_index` (query, optional): Specific sheet index

**Response:**
```json
[
  {
    "id": "image-uuid",
    "name": "chart.png",
    "image_type": "floating",
    "position": {"x": 100, "y": 200},
    "size": {"width": 300, "height": 200},
    "url": "image-uuid.png",
    "sheet_index": 0
  }
]
```

### Get Specific Image

Download a specific image.

**Endpoint:** `GET /api/v1/images/{file_id}/images/{image_id}`

**Parameters:**
- `file_id` (path): Unique file identifier
- `image_id` (path): Image identifier

**Response:** Binary image data with appropriate content-type header

### Upload Image

Upload and insert a new image into the Excel file.

**Endpoint:** `POST /api/v1/images/{file_id}/images`

**Content-Type:** `multipart/form-data`

**Parameters:**
- `image` (file): Image file
- `sheet_index` (form): Target sheet index
- `position_x` (form): X position
- `position_y` (form): Y position
- `width` (form, optional): Image width
- `height` (form, optional): Image height
- `image_type` (form, optional): Image type (floating, in_cell, background)

**Response:**
```json
{
  "id": "image-uuid",
  "name": "uploaded-image.png",
  "image_type": "floating",
  "position": {"x": 100, "y": 200},
  "size": {"width": 300, "height": 200},
  "url": "image-uuid.png",
  "sheet_index": 0
}
```

### Update Image

Update image position and size.

**Endpoint:** `PUT /api/v1/images/{file_id}/images/{image_id}`

**Content-Type:** `multipart/form-data`

**Parameters:**
- `position_x` (form, optional): New X position
- `position_y` (form, optional): New Y position
- `width` (form, optional): New width
- `height` (form, optional): New height

**Response:** Updated image data

### Delete Image

Delete an image from the Excel file.

**Endpoint:** `DELETE /api/v1/images/{file_id}/images/{image_id}`

**Response:**
```json
{
  "message": "Image deleted successfully"
}
```

## Health and Monitoring

### Health Check

Check application health status.

**Endpoint:** `GET /health`

**Response:**
```json
{
  "status": "healthy",
  "app_name": "Excel Web Application",
  "version": "1.0.0",
  "environment": "development"
}
```

### Root Endpoint

Get basic API information.

**Endpoint:** `GET /`

**Response:**
```json
{
  "message": "Excel Web Application API",
  "docs": "/docs",
  "health": "/health"
}
```

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **File uploads**: 5 uploads per minute per IP
- **Cell updates**: 100 updates per minute per file
- **General requests**: 1000 requests per minute per IP

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset time

## WebSocket Support

For real-time updates, the application supports WebSocket connections:

**Endpoint:** `ws://localhost:8000/ws/{file_id}`

**Events:**
- `cell_updated`: Cell value changed
- `sheet_changed`: Active sheet changed
- `file_processing`: Processing status updates

## SDK and Client Libraries

### Python Client Example

```python
import requests

# Upload file
with open('example.xlsx', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/api/v1/files/upload',
        files={'file': f}
    )
    file_id = response.json()['file_id']

# Get preview
preview = requests.get(
    f'http://localhost:8000/api/v1/excel/{file_id}/preview'
).json()

# Update cell
requests.put(
    f'http://localhost:8000/api/v1/excel/{file_id}/cells',
    json={
        'sheet_index': 0,
        'row': 0,
        'col': 0,
        'value': 'Updated Value'
    }
)
```

### JavaScript Client Example

```javascript
// Upload file
const formData = new FormData();
formData.append('file', fileInput.files[0]);

const uploadResponse = await fetch('/api/v1/files/upload', {
  method: 'POST',
  body: formData
});

const { file_id } = await uploadResponse.json();

// Get preview
const previewResponse = await fetch(`/api/v1/excel/${file_id}/preview`);
const preview = await previewResponse.json();

// Update cell
await fetch(`/api/v1/excel/${file_id}/cells`, {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    sheet_index: 0,
    row: 0,
    col: 0,
    value: 'Updated Value'
  })
});
```
