"""
Redis client configuration and utilities.
"""

import json
from typing import Any, Optional, Union
import aioredis
import structlog

from app.core.config import settings

logger = structlog.get_logger(__name__)


class RedisClient:
    """Redis client wrapper with caching utilities."""
    
    def __init__(self):
        self.redis: Optional[aioredis.Redis] = None
    
    async def initialize(self) -> None:
        """Initialize Redis connection."""
        try:
            self.redis = aioredis.from_url(
                settings.redis_url,
                encoding="utf-8",
                decode_responses=True,
                max_connections=20
            )
            # Test connection
            await self.redis.ping()
            logger.info("Redis connection established")
        except Exception as e:
            logger.error("Failed to connect to Red<PERSON>", error=str(e))
            self.redis = None
    
    async def close(self) -> None:
        """Close Redis connection."""
        if self.redis:
            await self.redis.close()
            logger.info("Redis connection closed")
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from Redis."""
        if not self.redis:
            return None
        
        try:
            value = await self.redis.get(key)
            if value:
                return json.loads(value)
            return None
        except Exception as e:
            logger.error("Redis GET error", key=key, error=str(e))
            return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None
    ) -> bool:
        """Set value in Redis."""
        if not self.redis:
            return False
        
        try:
            serialized_value = json.dumps(value, default=str)
            if ttl:
                await self.redis.setex(key, ttl, serialized_value)
            else:
                await self.redis.set(key, serialized_value)
            return True
        except Exception as e:
            logger.error("Redis SET error", key=key, error=str(e))
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from Redis."""
        if not self.redis:
            return False
        
        try:
            result = await self.redis.delete(key)
            return result > 0
        except Exception as e:
            logger.error("Redis DELETE error", key=key, error=str(e))
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in Redis."""
        if not self.redis:
            return False
        
        try:
            result = await self.redis.exists(key)
            return result > 0
        except Exception as e:
            logger.error("Redis EXISTS error", key=key, error=str(e))
            return False
    
    async def cache_file_conversion(
        self, 
        file_hash: str, 
        conversion_result: dict,
        ttl: Optional[int] = None
    ) -> bool:
        """Cache file conversion result."""
        cache_key = f"conversion:{file_hash}"
        return await self.set(
            cache_key, 
            conversion_result, 
            ttl or settings.CACHE_TTL
        )
    
    async def get_cached_conversion(self, file_hash: str) -> Optional[dict]:
        """Get cached file conversion result."""
        cache_key = f"conversion:{file_hash}"
        return await self.get(cache_key)
    
    async def cache_file_metadata(
        self, 
        file_id: str, 
        metadata: dict,
        ttl: Optional[int] = None
    ) -> bool:
        """Cache file metadata."""
        cache_key = f"metadata:{file_id}"
        return await self.set(
            cache_key, 
            metadata, 
            ttl or settings.CACHE_TTL
        )
    
    async def get_cached_metadata(self, file_id: str) -> Optional[dict]:
        """Get cached file metadata."""
        cache_key = f"metadata:{file_id}"
        return await self.get(cache_key)


# Global Redis client instance
redis_client = RedisClient()
