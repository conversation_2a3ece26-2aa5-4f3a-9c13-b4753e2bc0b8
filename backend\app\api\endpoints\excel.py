"""
Excel-specific endpoints for preview, editing, and sheet management.
"""

from typing import Optional
from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import HTMLResponse
import structlog

from app.models.excel import (
    ExcelPreviewResponse, 
    CellUpdateRequest, 
    SheetData,
    CellData
)
from app.services.excel_service import excel_service
from app.services.file_service import file_service

logger = structlog.get_logger(__name__)
router = APIRouter()


@router.get("/{file_id}/preview", response_model=ExcelPreviewResponse)
async def get_excel_preview(
    file_id: str,
    sheet_index: Optional[int] = Query(0, description="Sheet index to preview")
):
    """
    Get Excel file preview with HTML rendering.
    """
    try:
        # Check if file exists and is ready
        metadata = await file_service.get_metadata(file_id)
        if not metadata:
            raise HTTPException(status_code=404, detail="File not found")
        
        if metadata.status != "ready":
            raise HTTPException(
                status_code=400, 
                detail=f"File is not ready for preview. Status: {metadata.status}"
            )
        
        # Validate sheet index
        if sheet_index >= len(metadata.sheets):
            raise HTTPException(status_code=400, detail="Invalid sheet index")
        
        # Get HTML preview
        html_preview = await excel_service.get_html_preview(file_id, sheet_index)
        
        return ExcelPreviewResponse(
            file_id=file_id,
            metadata=metadata,
            html_preview=html_preview,
            current_sheet=sheet_index
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get Excel preview", file_id=file_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get Excel preview")


@router.get("/{file_id}/preview/html", response_class=HTMLResponse)
async def get_excel_html_preview(
    file_id: str,
    sheet_index: Optional[int] = Query(0, description="Sheet index to preview")
):
    """
    Get raw HTML preview of Excel file.
    """
    try:
        html_content = await excel_service.get_html_preview(file_id, sheet_index)
        if not html_content:
            raise HTTPException(status_code=404, detail="Preview not available")
        
        return HTMLResponse(content=html_content)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get HTML preview", file_id=file_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get HTML preview")


@router.get("/{file_id}/sheets", response_model=list[SheetData])
async def get_sheets(file_id: str):
    """
    Get all sheets information for a file.
    """
    try:
        metadata = await file_service.get_metadata(file_id)
        if not metadata:
            raise HTTPException(status_code=404, detail="File not found")
        
        return metadata.sheets
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get sheets", file_id=file_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get sheets")


@router.get("/{file_id}/sheets/{sheet_index}", response_model=SheetData)
async def get_sheet_data(file_id: str, sheet_index: int):
    """
    Get detailed data for a specific sheet.
    """
    try:
        sheet_data = await excel_service.get_sheet_data(file_id, sheet_index)
        if not sheet_data:
            raise HTTPException(status_code=404, detail="Sheet not found")
        
        return sheet_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get sheet data", file_id=file_id, sheet_index=sheet_index, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get sheet data")


@router.put("/{file_id}/cells", response_model=CellData)
async def update_cell(file_id: str, cell_update: CellUpdateRequest):
    """
    Update a cell value or formula.
    """
    try:
        # Validate file exists and is ready
        metadata = await file_service.get_metadata(file_id)
        if not metadata:
            raise HTTPException(status_code=404, detail="File not found")
        
        if metadata.status != "ready":
            raise HTTPException(
                status_code=400, 
                detail=f"File is not ready for editing. Status: {metadata.status}"
            )
        
        # Update cell
        updated_cell = await excel_service.update_cell(file_id, cell_update)
        
        logger.info(
            "Cell updated successfully", 
            file_id=file_id, 
            sheet=cell_update.sheet_index,
            row=cell_update.row,
            col=cell_update.col
        )
        
        return updated_cell
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update cell", file_id=file_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to update cell")


@router.get("/{file_id}/formulas")
async def get_formulas(file_id: str, sheet_index: Optional[int] = Query(None)):
    """
    Get all formulas in the file or specific sheet.
    """
    try:
        formulas = await excel_service.extract_formulas(file_id, sheet_index)
        return {"formulas": formulas}
        
    except Exception as e:
        logger.error("Failed to get formulas", file_id=file_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get formulas")


@router.post("/{file_id}/recalculate")
async def recalculate_formulas(file_id: str):
    """
    Recalculate all formulas in the Excel file.
    """
    try:
        success = await excel_service.recalculate_formulas(file_id)
        if not success:
            raise HTTPException(status_code=400, detail="Failed to recalculate formulas")
        
        return {"message": "Formulas recalculated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to recalculate formulas", file_id=file_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to recalculate formulas")
