import React, { useMemo } from 'react';
import { AgGridReact } from 'ag-grid-react';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import { ColDef, GridReadyEvent, CellClickedEvent } from 'ag-grid-community';
import { SheetData, CellData } from '../services/api';

interface ExcelGridProps {
  sheetData: SheetData;
  selectedCell: { row: number; col: number } | null;
  onCellSelect: (row: number, col: number) => void;
  fileId: string;
}

const ExcelGrid: React.FC<ExcelGridProps> = ({ 
  sheetData, 
  selectedCell, 
  onCellSelect,
  fileId 
}) => {
  // Convert sheet data to AG Grid format
  const { columnDefs, rowData } = useMemo(() => {
    if (!sheetData || sheetData.cells.length === 0) {
      return { columnDefs: [], rowData: [] };
    }

    // Find max columns and rows
    const maxCol = Math.max(...sheetData.cells.map(cell => cell.col)) + 1;
    const maxRow = Math.max(...sheetData.cells.map(cell => cell.row)) + 1;

    // Create column definitions
    const cols: ColDef[] = [
      {
        headerName: '',
        field: 'rowNumber',
        width: 60,
        pinned: 'left',
        cellStyle: { 
          backgroundColor: '#f8f9fa', 
          fontWeight: 'bold',
          textAlign: 'center',
          border: '1px solid #dee2e6'
        },
        suppressMovable: true,
        sortable: false,
        filter: false,
      }
    ];

    // Add data columns
    for (let col = 0; col < Math.min(maxCol, 50); col++) { // Limit to 50 columns for performance
      const columnLetter = getColumnLetter(col);
      cols.push({
        headerName: columnLetter,
        field: `col_${col}`,
        width: 120,
        editable: true,
        cellStyle: (params) => {
          const style: any = {
            border: '1px solid #dee2e6',
          };
          
          // Highlight selected cell
          if (selectedCell && 
              params.node?.rowIndex === selectedCell.row && 
              params.colDef?.field === `col_${selectedCell.col}`) {
            style.backgroundColor = '#e3f2fd';
            style.border = '2px solid #2196f3';
          }
          
          return style;
        },
        valueFormatter: (params) => {
          const cell = findCell(sheetData.cells, params.node?.rowIndex || 0, col);
          if (cell?.formula) {
            return cell.value?.toString() || '';
          }
          return params.value || '';
        },
        tooltipValueGetter: (params) => {
          const cell = findCell(sheetData.cells, params.node?.rowIndex || 0, col);
          if (cell?.formula) {
            return `Formula: ${cell.formula}\nValue: ${cell.value || ''}`;
          }
          return params.value || '';
        },
      });
    }

    // Create row data
    const rows: any[] = [];
    for (let row = 0; row < Math.min(maxRow, 1000); row++) { // Limit to 1000 rows for performance
      const rowObj: any = {
        rowNumber: row + 1,
      };

      // Fill in cell values
      for (let col = 0; col < Math.min(maxCol, 50); col++) {
        const cell = findCell(sheetData.cells, row, col);
        rowObj[`col_${col}`] = cell?.value || '';
      }

      rows.push(rowObj);
    }

    return { columnDefs: cols, rowData: rows };
  }, [sheetData, selectedCell]);

  const findCell = (cells: CellData[], row: number, col: number): CellData | undefined => {
    return cells.find(cell => cell.row === row && cell.col === col);
  };

  const getColumnLetter = (col: number): string => {
    let result = '';
    while (col >= 0) {
      result = String.fromCharCode(65 + (col % 26)) + result;
      col = Math.floor(col / 26) - 1;
    }
    return result;
  };

  const onCellClicked = (event: CellClickedEvent) => {
    if (event.colDef?.field?.startsWith('col_')) {
      const col = parseInt(event.colDef.field.split('_')[1]);
      const row = event.node?.rowIndex || 0;
      onCellSelect(row, col);
    }
  };

  const onGridReady = (params: GridReadyEvent) => {
    params.api.sizeColumnsToFit();
  };

  if (!sheetData || sheetData.cells.length === 0) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%',
        color: '#6c757d'
      }}>
        <div>
          <h4>Empty Sheet</h4>
          <p>This sheet contains no data</p>
        </div>
      </div>
    );
  }

  return (
    <div 
      className="ag-theme-alpine" 
      style={{ 
        height: '100%', 
        width: '100%',
        '--ag-border-color': '#dee2e6',
        '--ag-header-background-color': '#f8f9fa',
        '--ag-odd-row-background-color': '#ffffff',
        '--ag-even-row-background-color': '#f9f9f9',
      } as React.CSSProperties}
    >
      <AgGridReact
        columnDefs={columnDefs}
        rowData={rowData}
        onGridReady={onGridReady}
        onCellClicked={onCellClicked}
        suppressRowClickSelection={true}
        suppressCellFocus={false}
        enableCellTextSelection={true}
        suppressMovableColumns={true}
        suppressColumnVirtualisation={false}
        suppressRowVirtualisation={false}
        rowHeight={25}
        headerHeight={30}
        animateRows={false}
        enableRangeSelection={true}
        suppressMultiRangeSelection={false}
        defaultColDef={{
          resizable: true,
          sortable: false,
          filter: false,
        }}
      />
    </div>
  );
};

export default ExcelGrid;
