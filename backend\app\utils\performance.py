"""
Performance monitoring and optimization utilities.
"""

import asyncio
import psutil
import time
from typing import Dict, Any
import structlog

from app.core.config import settings

logger = structlog.get_logger(__name__)


class PerformanceMonitor:
    """Monitor system performance and resource usage."""
    
    def __init__(self):
        self.start_time = time.time()
        self.request_count = 0
        self.error_count = 0
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get current system statistics."""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                "cpu_percent": cpu_percent,
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "percent": memory.percent,
                    "used": memory.used,
                    "free": memory.free
                },
                "disk": {
                    "total": disk.total,
                    "used": disk.used,
                    "free": disk.free,
                    "percent": (disk.used / disk.total) * 100
                },
                "uptime": time.time() - self.start_time,
                "requests": self.request_count,
                "errors": self.error_count
            }
        except Exception as e:
            logger.error("Failed to get system stats", error=str(e))
            return {}
    
    def increment_request_count(self):
        """Increment request counter."""
        self.request_count += 1
    
    def increment_error_count(self):
        """Increment error counter."""
        self.error_count += 1
    
    async def check_resource_limits(self) -> Dict[str, bool]:
        """Check if system resources are within acceptable limits."""
        try:
            stats = self.get_system_stats()
            
            return {
                "cpu_ok": stats.get("cpu_percent", 0) < 80,
                "memory_ok": stats.get("memory", {}).get("percent", 0) < 85,
                "disk_ok": stats.get("disk", {}).get("percent", 0) < 90
            }
        except Exception as e:
            logger.error("Failed to check resource limits", error=str(e))
            return {"cpu_ok": True, "memory_ok": True, "disk_ok": True}


class AsyncLimiter:
    """Async rate limiter for controlling concurrent operations."""
    
    def __init__(self, max_concurrent: int = 5, max_per_second: float = 10.0):
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.max_per_second = max_per_second
        self.last_call_time = 0.0
        self.call_times = []
    
    async def acquire(self):
        """Acquire rate limit permission."""
        await self.semaphore.acquire()
        
        current_time = time.time()
        
        # Remove old call times (older than 1 second)
        self.call_times = [t for t in self.call_times if current_time - t < 1.0]
        
        # Check if we're within rate limit
        if len(self.call_times) >= self.max_per_second:
            sleep_time = 1.0 - (current_time - self.call_times[0])
            if sleep_time > 0:
                await asyncio.sleep(sleep_time)
        
        self.call_times.append(current_time)
        self.last_call_time = current_time
    
    def release(self):
        """Release rate limit permission."""
        self.semaphore.release()
    
    async def __aenter__(self):
        await self.acquire()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        self.release()


class CacheManager:
    """Manage caching strategies and cache warming."""
    
    def __init__(self):
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0
        }
    
    def record_hit(self):
        """Record cache hit."""
        self.cache_stats["hits"] += 1
    
    def record_miss(self):
        """Record cache miss."""
        self.cache_stats["misses"] += 1
    
    def record_eviction(self):
        """Record cache eviction."""
        self.cache_stats["evictions"] += 1
    
    def get_hit_ratio(self) -> float:
        """Calculate cache hit ratio."""
        total = self.cache_stats["hits"] + self.cache_stats["misses"]
        if total == 0:
            return 0.0
        return self.cache_stats["hits"] / total
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            **self.cache_stats,
            "hit_ratio": self.get_hit_ratio()
        }


class ConnectionPool:
    """Manage database/external service connection pooling."""
    
    def __init__(self, max_connections: int = 20):
        self.max_connections = max_connections
        self.active_connections = 0
        self.total_connections = 0
        self.connection_errors = 0
    
    async def acquire_connection(self):
        """Acquire a connection from the pool."""
        if self.active_connections >= self.max_connections:
            raise Exception("Connection pool exhausted")
        
        self.active_connections += 1
        self.total_connections += 1
        return self
    
    async def release_connection(self):
        """Release a connection back to the pool."""
        self.active_connections = max(0, self.active_connections - 1)
    
    def record_error(self):
        """Record connection error."""
        self.connection_errors += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """Get connection pool statistics."""
        return {
            "active_connections": self.active_connections,
            "total_connections": self.total_connections,
            "connection_errors": self.connection_errors,
            "max_connections": self.max_connections,
            "utilization": self.active_connections / self.max_connections
        }


# Global instances
performance_monitor = PerformanceMonitor()
excel_limiter = AsyncLimiter(max_concurrent=settings.MAX_CONCURRENT_CONVERSIONS, max_per_second=5.0)
cache_manager = CacheManager()
connection_pool = ConnectionPool()


async def optimize_for_memory():
    """Perform memory optimization tasks."""
    try:
        import gc
        
        # Force garbage collection
        collected = gc.collect()
        
        # Get memory stats
        stats = performance_monitor.get_system_stats()
        memory_percent = stats.get("memory", {}).get("percent", 0)
        
        if memory_percent > 80:
            logger.warning("High memory usage detected", memory_percent=memory_percent)
            
            # Additional cleanup could be performed here
            # For example: clear caches, close unused connections, etc.
        
        logger.info("Memory optimization completed", 
                   collected_objects=collected, 
                   memory_percent=memory_percent)
        
    except Exception as e:
        logger.error("Memory optimization failed", error=str(e))


async def health_check() -> Dict[str, Any]:
    """Comprehensive health check."""
    try:
        system_stats = performance_monitor.get_system_stats()
        resource_limits = await performance_monitor.check_resource_limits()
        cache_stats = cache_manager.get_stats()
        pool_stats = connection_pool.get_stats()
        
        # Overall health status
        is_healthy = all(resource_limits.values())
        
        return {
            "healthy": is_healthy,
            "system": system_stats,
            "resources": resource_limits,
            "cache": cache_stats,
            "connections": pool_stats,
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        return {
            "healthy": False,
            "error": str(e),
            "timestamp": time.time()
        }
