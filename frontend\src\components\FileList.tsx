import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import { FiEye, FiTrash2, FiClock, FiFile, FiImage, FiCalculator } from 'react-icons/fi';
import toast from 'react-hot-toast';
import { fileApi, FileMetadata } from '../services/api';

interface FileListProps {
  onFileSelect?: (fileId: string) => void;
}

const FileList: React.FC<FileListProps> = ({ onFileSelect }) => {
  const queryClient = useQueryClient();

  const { data: files = [], isLoading, error } = useQuery({
    queryKey: ['files'],
    queryFn: fileApi.listFiles,
    refetchInterval: 5000, // Refresh every 5 seconds to check processing status
  });

  const deleteMutation = useMutation({
    mutationFn: fileApi.deleteFile,
    onSuccess: () => {
      toast.success('File deleted successfully');
      queryClient.invalidateQueries({ queryKey: ['files'] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || 'Failed to delete file');
    },
  });

  const handleDelete = (fileId: string, filename: string) => {
    if (window.confirm(`Are you sure you want to delete "${filename}"?`)) {
      deleteMutation.mutate(fileId);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'ready': return '#28a745';
      case 'processing': return '#ffc107';
      case 'error': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ready': return '✅';
      case 'processing': return '⏳';
      case 'error': return '❌';
      default: return '⏸️';
    }
  };

  if (isLoading) {
    return <div className="loading">Loading files...</div>;
  }

  if (error) {
    return <div className="error">Failed to load files</div>;
  }

  if (files.length === 0) {
    return (
      <div style={{ textAlign: 'center', padding: '2rem', color: '#6c757d' }}>
        <FiFile size={48} style={{ marginBottom: '1rem' }} />
        <h3>No files uploaded yet</h3>
        <p>Upload your first Excel file to get started</p>
      </div>
    );
  }

  return (
    <div style={{ padding: '1rem' }}>
      <h2 style={{ marginBottom: '1rem', display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
        <FiFile size={20} />
        Uploaded Files ({files.length})
      </h2>
      
      <div style={{ display: 'grid', gap: '1rem' }}>
        {files.map((file: FileMetadata) => (
          <div
            key={file.file_id}
            style={{
              border: '1px solid #dee2e6',
              borderRadius: '8px',
              padding: '1rem',
              backgroundColor: 'white',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            }}
          >
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '0.5rem' }}>
              <div style={{ flex: 1 }}>
                <h4 style={{ margin: '0 0 0.5rem 0', display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                  <FiFile size={16} />
                  {file.filename}
                </h4>
                
                <div style={{ display: 'flex', gap: '1rem', fontSize: '0.9rem', color: '#6c757d', marginBottom: '0.5rem' }}>
                  <span>Size: {formatFileSize(file.file_size)}</span>
                  <span>Sheets: {file.total_sheets}</span>
                  {file.has_formulas && (
                    <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                      <FiCalculator size={12} />
                      Formulas
                    </span>
                  )}
                  {file.has_images && (
                    <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                      <FiImage size={12} />
                      Images
                    </span>
                  )}
                </div>
                
                <div style={{ fontSize: '0.8rem', color: '#6c757d', display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                  <FiClock size={12} />
                  Uploaded: {formatDate(file.upload_time)}
                </div>
              </div>
              
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <span
                  style={{
                    padding: '0.25rem 0.5rem',
                    borderRadius: '4px',
                    fontSize: '0.8rem',
                    backgroundColor: getStatusColor(file.status) + '20',
                    color: getStatusColor(file.status),
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.25rem',
                  }}
                >
                  {getStatusIcon(file.status)}
                  {file.status.charAt(0).toUpperCase() + file.status.slice(1)}
                </span>
              </div>
            </div>
            
            {file.error_message && (
              <div style={{ 
                padding: '0.5rem', 
                backgroundColor: '#f8d7da', 
                color: '#721c24', 
                borderRadius: '4px', 
                fontSize: '0.9rem',
                marginBottom: '0.5rem'
              }}>
                Error: {file.error_message}
              </div>
            )}
            
            <div style={{ display: 'flex', gap: '0.5rem', justifyContent: 'flex-end' }}>
              {file.status === 'ready' && (
                <Link
                  to={`/excel/${file.file_id}`}
                  className="btn btn-primary"
                  style={{ textDecoration: 'none', display: 'flex', alignItems: 'center', gap: '0.5rem' }}
                  onClick={() => onFileSelect?.(file.file_id)}
                >
                  <FiEye size={14} />
                  View
                </Link>
              )}
              
              <button
                className="btn btn-danger"
                onClick={() => handleDelete(file.file_id, file.filename)}
                disabled={deleteMutation.isPending}
                style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}
              >
                <FiTrash2 size={14} />
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FileList;
