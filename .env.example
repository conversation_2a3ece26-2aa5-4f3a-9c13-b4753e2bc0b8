# Application Configuration
APP_NAME=Excel Web Application
APP_VERSION=1.0.0
DEBUG=true
ENVIRONMENT=development

# Server Configuration
HOST=0.0.0.0
PORT=8000
WORKERS=1

# Database Configuration (if needed for user sessions/metadata)
DATABASE_URL=sqlite:///./excel_app.db

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# File Storage Configuration
UPLOAD_DIR=./uploads
TEMP_DIR=./temp
MAX_FILE_SIZE=100MB
ALLOWED_EXTENSIONS=.xls,.xlsx

# LibreOffice Configuration
LIBREOFFICE_PATH=/usr/bin/libreoffice
LIBREOFFICE_TIMEOUT=30
LIBREOFFICE_HEADLESS=true

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# Security
SECRET_KEY=your-secret-key-here
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Performance
ASYNC_WORKERS=4
MAX_CONCURRENT_CONVERSIONS=5

# Cleanup Configuration
CLEANUP_INTERVAL=3600
TEMP_FILE_MAX_AGE=7200
