"""
Integration tests for API endpoints.
"""

import pytest
from httpx import AsyncC<PERSON>
from fastapi import status
import json
import io


class TestFileEndpoints:
    """Test cases for file-related endpoints."""
    
    @pytest.mark.asyncio
    async def test_health_check(self, async_client: AsyncClient):
        """Test health check endpoint."""
        response = await async_client.get("/health")
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["status"] == "healthy"
        assert "app_name" in data
        assert "version" in data
    
    @pytest.mark.asyncio
    async def test_root_endpoint(self, async_client: AsyncClient):
        """Test root endpoint."""
        response = await async_client.get("/")
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert "message" in data
        assert "docs" in data
    
    @pytest.mark.asyncio
    async def test_upload_file_success(self, async_client: AsyncClient, sample_excel_file):
        """Test successful file upload."""
        with open(sample_excel_file, "rb") as f:
            files = {"file": ("test.xlsx", f, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")}
            response = await async_client.post("/api/v1/files/upload", files=files)
        
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert "file_id" in data
        assert data["status"] == "processing"
        assert "message" in data
    
    @pytest.mark.asyncio
    async def test_upload_invalid_file_type(self, async_client: AsyncClient):
        """Test uploading invalid file type."""
        # Create a text file instead of Excel
        content = b"This is not an Excel file"
        files = {"file": ("test.txt", io.BytesIO(content), "text/plain")}
        
        response = await async_client.post("/api/v1/files/upload", files=files)
        assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    @pytest.mark.asyncio
    async def test_upload_oversized_file(self, async_client: AsyncClient):
        """Test uploading oversized file."""
        # Create a large file (simulate by setting large content-length)
        content = b"x" * 1000  # Small content but we'll mock the size
        files = {"file": ("large.xlsx", io.BytesIO(content), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")}
        
        # This test would need to mock the file size validation
        # For now, we'll test with a normal file
        response = await async_client.post("/api/v1/files/upload", files=files)
        # The actual validation happens in the file validation function
        # This test serves as a placeholder for size validation testing
    
    @pytest.mark.asyncio
    async def test_get_file_status_not_found(self, async_client: AsyncClient):
        """Test getting status of non-existent file."""
        response = await async_client.get("/api/v1/files/status/nonexistent-file")
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    @pytest.mark.asyncio
    async def test_list_files_empty(self, async_client: AsyncClient):
        """Test listing files when none exist."""
        response = await async_client.get("/api/v1/files/list")
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert isinstance(data, list)
    
    @pytest.mark.asyncio
    async def test_delete_nonexistent_file(self, async_client: AsyncClient):
        """Test deleting non-existent file."""
        response = await async_client.delete("/api/v1/files/nonexistent-file")
        assert response.status_code == status.HTTP_404_NOT_FOUND


class TestExcelEndpoints:
    """Test cases for Excel-related endpoints."""
    
    @pytest.mark.asyncio
    async def test_get_preview_not_found(self, async_client: AsyncClient):
        """Test getting preview of non-existent file."""
        response = await async_client.get("/api/v1/excel/nonexistent-file/preview")
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    @pytest.mark.asyncio
    async def test_get_sheets_not_found(self, async_client: AsyncClient):
        """Test getting sheets of non-existent file."""
        response = await async_client.get("/api/v1/excel/nonexistent-file/sheets")
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    @pytest.mark.asyncio
    async def test_get_sheet_data_not_found(self, async_client: AsyncClient):
        """Test getting sheet data of non-existent file."""
        response = await async_client.get("/api/v1/excel/nonexistent-file/sheets/0")
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    @pytest.mark.asyncio
    async def test_update_cell_not_found(self, async_client: AsyncClient):
        """Test updating cell in non-existent file."""
        cell_update = {
            "sheet_index": 0,
            "row": 0,
            "col": 0,
            "value": "test"
        }
        
        response = await async_client.put(
            "/api/v1/excel/nonexistent-file/cells",
            json=cell_update
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    @pytest.mark.asyncio
    async def test_get_formulas_not_found(self, async_client: AsyncClient):
        """Test getting formulas from non-existent file."""
        response = await async_client.get("/api/v1/excel/nonexistent-file/formulas")
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    
    @pytest.mark.asyncio
    async def test_recalculate_formulas_not_found(self, async_client: AsyncClient):
        """Test recalculating formulas in non-existent file."""
        response = await async_client.post("/api/v1/excel/nonexistent-file/recalculate")
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR


class TestImageEndpoints:
    """Test cases for image-related endpoints."""
    
    @pytest.mark.asyncio
    async def test_get_images_not_found(self, async_client: AsyncClient):
        """Test getting images from non-existent file."""
        response = await async_client.get("/api/v1/images/nonexistent-file/images")
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    
    @pytest.mark.asyncio
    async def test_get_image_not_found(self, async_client: AsyncClient):
        """Test getting specific image from non-existent file."""
        response = await async_client.get("/api/v1/images/nonexistent-file/images/nonexistent-image")
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    
    @pytest.mark.asyncio
    async def test_upload_image_invalid_file(self, async_client: AsyncClient):
        """Test uploading invalid image file."""
        # Create a text file instead of image
        content = b"This is not an image"
        files = {"image": ("test.txt", io.BytesIO(content), "text/plain")}
        data = {
            "sheet_index": 0,
            "position_x": 100,
            "position_y": 100
        }
        
        response = await async_client.post(
            "/api/v1/images/nonexistent-file/images",
            files=files,
            data=data
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    @pytest.mark.asyncio
    async def test_delete_image_not_found(self, async_client: AsyncClient):
        """Test deleting non-existent image."""
        response = await async_client.delete("/api/v1/images/nonexistent-file/images/nonexistent-image")
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR


class TestErrorHandling:
    """Test cases for error handling."""
    
    @pytest.mark.asyncio
    async def test_invalid_json_payload(self, async_client: AsyncClient):
        """Test sending invalid JSON payload."""
        response = await async_client.put(
            "/api/v1/excel/test-file/cells",
            content="invalid json",
            headers={"content-type": "application/json"}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    @pytest.mark.asyncio
    async def test_missing_required_fields(self, async_client: AsyncClient):
        """Test sending request with missing required fields."""
        incomplete_data = {
            "sheet_index": 0,
            # Missing row, col, and value
        }
        
        response = await async_client.put(
            "/api/v1/excel/test-file/cells",
            json=incomplete_data
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    @pytest.mark.asyncio
    async def test_invalid_file_id_format(self, async_client: AsyncClient):
        """Test using invalid file ID format."""
        # Test with various invalid file IDs
        invalid_ids = ["", "   ", "invalid/id", "id with spaces"]
        
        for invalid_id in invalid_ids:
            response = await async_client.get(f"/api/v1/files/status/{invalid_id}")
            # The response might be 404 or 422 depending on validation
            assert response.status_code in [status.HTTP_404_NOT_FOUND, status.HTTP_422_UNPROCESSABLE_ENTITY]
