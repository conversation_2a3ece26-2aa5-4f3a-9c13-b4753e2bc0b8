"""
File management service for handling uploads, storage, and metadata.
"""

import os
import json
import time
import aiofiles
from typing import List, Optional
from pathlib import Path
import structlog

from app.core.config import settings
from app.core.redis import redis_client
from app.models.excel import ExcelFileMetadata
from app.utils.file_utils import ensure_directory_exists

logger = structlog.get_logger(__name__)


class FileService:
    """Service for file management operations."""
    
    def __init__(self):
        self.upload_dir = Path(settings.UPLOAD_DIR)
        self.temp_dir = Path(settings.TEMP_DIR)
        self.metadata_dir = self.upload_dir / "metadata"
    
    async def initialize(self):
        """Initialize file service directories."""
        await ensure_directory_exists(self.upload_dir)
        await ensure_directory_exists(self.temp_dir)
        await ensure_directory_exists(self.metadata_dir)
    
    async def save_uploaded_file(self, file, file_id: str) -> str:
        """Save uploaded file to disk."""
        try:
            # Ensure upload directory exists
            await ensure_directory_exists(self.upload_dir)
            
            # Create file path
            file_extension = Path(file.filename).suffix
            file_path = self.upload_dir / f"{file_id}{file_extension}"
            
            # Save file
            async with aiofiles.open(file_path, 'wb') as f:
                content = await file.read()
                await f.write(content)
            
            logger.info("File saved successfully", file_id=file_id, path=str(file_path))
            return str(file_path)
            
        except Exception as e:
            logger.error("Failed to save uploaded file", file_id=file_id, error=str(e))
            raise
    
    async def save_metadata(self, file_id: str, metadata: ExcelFileMetadata) -> bool:
        """Save file metadata."""
        try:
            # Save to Redis cache
            await redis_client.cache_file_metadata(file_id, metadata.model_dump())
            
            # Also save to disk as backup
            metadata_path = self.metadata_dir / f"{file_id}.json"
            async with aiofiles.open(metadata_path, 'w') as f:
                await f.write(metadata.model_dump_json(indent=2))
            
            logger.info("Metadata saved successfully", file_id=file_id)
            return True
            
        except Exception as e:
            logger.error("Failed to save metadata", file_id=file_id, error=str(e))
            return False
    
    async def get_metadata(self, file_id: str) -> Optional[ExcelFileMetadata]:
        """Get file metadata."""
        try:
            # Try Redis first
            cached_metadata = await redis_client.get_cached_metadata(file_id)
            if cached_metadata:
                return ExcelFileMetadata(**cached_metadata)
            
            # Fallback to disk
            metadata_path = self.metadata_dir / f"{file_id}.json"
            if metadata_path.exists():
                async with aiofiles.open(metadata_path, 'r') as f:
                    content = await f.read()
                    metadata_dict = json.loads(content)
                    metadata = ExcelFileMetadata(**metadata_dict)
                    
                    # Cache it back to Redis
                    await redis_client.cache_file_metadata(file_id, metadata_dict)
                    
                    return metadata
            
            return None
            
        except Exception as e:
            logger.error("Failed to get metadata", file_id=file_id, error=str(e))
            return None
    
    async def update_metadata(self, file_id: str, updates: dict) -> bool:
        """Update file metadata."""
        try:
            metadata = await self.get_metadata(file_id)
            if not metadata:
                return False
            
            # Update metadata
            metadata_dict = metadata.model_dump()
            metadata_dict.update(updates)
            updated_metadata = ExcelFileMetadata(**metadata_dict)
            
            # Save updated metadata
            return await self.save_metadata(file_id, updated_metadata)
            
        except Exception as e:
            logger.error("Failed to update metadata", file_id=file_id, error=str(e))
            return False
    
    async def list_files(self) -> List[ExcelFileMetadata]:
        """List all uploaded files."""
        try:
            files = []
            
            # Check metadata directory
            if self.metadata_dir.exists():
                for metadata_file in self.metadata_dir.glob("*.json"):
                    file_id = metadata_file.stem
                    metadata = await self.get_metadata(file_id)
                    if metadata:
                        files.append(metadata)
            
            # Sort by upload time (newest first)
            files.sort(key=lambda x: x.upload_time, reverse=True)
            
            return files
            
        except Exception as e:
            logger.error("Failed to list files", error=str(e))
            return []
    
    async def delete_file(self, file_id: str) -> bool:
        """Delete file and its metadata."""
        try:
            metadata = await self.get_metadata(file_id)
            if not metadata:
                return False
            
            # Delete file
            file_extension = Path(metadata.filename).suffix
            file_path = self.upload_dir / f"{file_id}{file_extension}"
            if file_path.exists():
                file_path.unlink()
            
            # Delete metadata file
            metadata_path = self.metadata_dir / f"{file_id}.json"
            if metadata_path.exists():
                metadata_path.unlink()
            
            # Delete from Redis
            await redis_client.delete(f"metadata:{file_id}")
            await redis_client.delete(f"conversion:{metadata.file_hash}")
            
            # Delete temp files
            temp_pattern = f"{file_id}*"
            for temp_file in self.temp_dir.glob(temp_pattern):
                temp_file.unlink()
            
            logger.info("File deleted successfully", file_id=file_id)
            return True
            
        except Exception as e:
            logger.error("Failed to delete file", file_id=file_id, error=str(e))
            return False
    
    async def get_file_path(self, file_id: str) -> Optional[str]:
        """Get the file path for a given file ID."""
        try:
            metadata = await self.get_metadata(file_id)
            if not metadata:
                return None
            
            file_extension = Path(metadata.filename).suffix
            file_path = self.upload_dir / f"{file_id}{file_extension}"
            
            if file_path.exists():
                return str(file_path)
            
            return None
            
        except Exception as e:
            logger.error("Failed to get file path", file_id=file_id, error=str(e))
            return None
    
    async def cleanup_temp_files(self, max_age_seconds: int = None) -> int:
        """Clean up old temporary files."""
        try:
            max_age = max_age_seconds or settings.TEMP_FILE_MAX_AGE
            current_time = time.time()
            cleaned_count = 0
            
            for temp_file in self.temp_dir.iterdir():
                if temp_file.is_file():
                    file_age = current_time - temp_file.stat().st_mtime
                    if file_age > max_age:
                        temp_file.unlink()
                        cleaned_count += 1
            
            if cleaned_count > 0:
                logger.info("Cleaned up temporary files", count=cleaned_count)
            
            return cleaned_count
            
        except Exception as e:
            logger.error("Failed to cleanup temp files", error=str(e))
            return 0


# Global file service instance
file_service = FileService()
