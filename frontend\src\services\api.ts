import axios from 'axios';

// API base configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || '/api/v1';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add any auth headers here if needed
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Handle unauthorized
    } else if (error.response?.status >= 500) {
      // Handle server errors
      console.error('Server error:', error.response.data);
    }
    return Promise.reject(error);
  }
);

// Types
export interface FileMetadata {
  file_id: string;
  filename: string;
  file_size: number;
  file_hash: string;
  mime_type: string;
  upload_time: string;
  status: 'uploading' | 'processing' | 'ready' | 'error';
  error_message?: string;
  sheets: SheetData[];
  total_sheets: number;
  has_formulas: boolean;
  has_images: boolean;
}

export interface SheetData {
  index: number;
  name: string;
  cells: CellData[];
  images: ImageData[];
  row_count: number;
  col_count: number;
  hidden: boolean;
}

export interface CellData {
  row: number;
  col: number;
  value?: string | number | boolean;
  formula?: string;
  cell_type: 'text' | 'number' | 'formula' | 'date' | 'boolean' | 'error' | 'empty';
  format?: string;
  style?: Record<string, any>;
}

export interface ImageData {
  id: string;
  name: string;
  image_type: 'in_cell' | 'floating' | 'background';
  position: { x: number; y: number };
  size: { width: number; height: number };
  data?: string;
  url?: string;
  sheet_index: number;
}

export interface ProcessingStatus {
  file_id: string;
  status: 'uploading' | 'processing' | 'ready' | 'error';
  progress: number;
  message: string;
  error?: string;
}

export interface ExcelPreviewResponse {
  file_id: string;
  metadata: FileMetadata;
  html_preview?: string;
  current_sheet: number;
}

export interface CellUpdateRequest {
  sheet_index: number;
  row: number;
  col: number;
  value?: string | number | boolean;
  formula?: string;
}

// API functions
export const fileApi = {
  // Upload file
  uploadFile: async (file: File): Promise<ProcessingStatus> => {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await api.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Get file status
  getFileStatus: async (fileId: string): Promise<ProcessingStatus> => {
    const response = await api.get(`/files/status/${fileId}`);
    return response.data;
  },

  // List files
  listFiles: async (): Promise<FileMetadata[]> => {
    const response = await api.get('/files/list');
    return response.data;
  },

  // Delete file
  deleteFile: async (fileId: string): Promise<void> => {
    await api.delete(`/files/${fileId}`);
  },

  // Get file metadata
  getFileMetadata: async (fileId: string): Promise<FileMetadata> => {
    const response = await api.get(`/files/${fileId}/metadata`);
    return response.data;
  },
};

export const excelApi = {
  // Get Excel preview
  getPreview: async (fileId: string, sheetIndex: number = 0): Promise<ExcelPreviewResponse> => {
    const response = await api.get(`/excel/${fileId}/preview?sheet_index=${sheetIndex}`);
    return response.data;
  },

  // Get HTML preview
  getHtmlPreview: async (fileId: string, sheetIndex: number = 0): Promise<string> => {
    const response = await api.get(`/excel/${fileId}/preview/html?sheet_index=${sheetIndex}`, {
      responseType: 'text',
    });
    return response.data;
  },

  // Get sheets
  getSheets: async (fileId: string): Promise<SheetData[]> => {
    const response = await api.get(`/excel/${fileId}/sheets`);
    return response.data;
  },

  // Get sheet data
  getSheetData: async (fileId: string, sheetIndex: number): Promise<SheetData> => {
    const response = await api.get(`/excel/${fileId}/sheets/${sheetIndex}`);
    return response.data;
  },

  // Update cell
  updateCell: async (fileId: string, cellUpdate: CellUpdateRequest): Promise<CellData> => {
    const response = await api.put(`/excel/${fileId}/cells`, cellUpdate);
    return response.data;
  },

  // Get formulas
  getFormulas: async (fileId: string, sheetIndex?: number): Promise<{ formulas: any[] }> => {
    const url = sheetIndex !== undefined 
      ? `/excel/${fileId}/formulas?sheet_index=${sheetIndex}`
      : `/excel/${fileId}/formulas`;
    const response = await api.get(url);
    return response.data;
  },

  // Recalculate formulas
  recalculateFormulas: async (fileId: string): Promise<{ message: string }> => {
    const response = await api.post(`/excel/${fileId}/recalculate`);
    return response.data;
  },
};

export const imageApi = {
  // Get images
  getImages: async (fileId: string, sheetIndex?: number): Promise<ImageData[]> => {
    const url = sheetIndex !== undefined 
      ? `/images/${fileId}/images?sheet_index=${sheetIndex}`
      : `/images/${fileId}/images`;
    const response = await api.get(url);
    return response.data;
  },

  // Upload image
  uploadImage: async (
    fileId: string, 
    image: File, 
    options: {
      sheetIndex: number;
      positionX: number;
      positionY: number;
      width?: number;
      height?: number;
      imageType?: 'in_cell' | 'floating' | 'background';
    }
  ): Promise<ImageData> => {
    const formData = new FormData();
    formData.append('image', image);
    formData.append('sheet_index', options.sheetIndex.toString());
    formData.append('position_x', options.positionX.toString());
    formData.append('position_y', options.positionY.toString());
    
    if (options.width) formData.append('width', options.width.toString());
    if (options.height) formData.append('height', options.height.toString());
    if (options.imageType) formData.append('image_type', options.imageType);

    const response = await api.post(`/images/${fileId}/images`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Delete image
  deleteImage: async (fileId: string, imageId: string): Promise<void> => {
    await api.delete(`/images/${fileId}/images/${imageId}`);
  },
};

export default api;
