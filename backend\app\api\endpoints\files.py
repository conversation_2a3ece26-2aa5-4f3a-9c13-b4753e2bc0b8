"""
File upload and management endpoints.
"""

import os
import uuid
from typing import List
from fastapi import APIRouter, File, HTTPException, UploadFile, BackgroundTasks
from fastapi.responses import JSONResponse
import structlog

from app.core.config import settings
from app.models.excel import ExcelFileMetadata, FileStatus, ProcessingStatus
from app.services.file_service import file_service
from app.services.excel_service import excel_service
from app.utils.file_utils import validate_file, calculate_file_hash

logger = structlog.get_logger(__name__)
router = APIRouter()


@router.post("/upload", response_model=ProcessingStatus)
async def upload_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...)
):
    """
    Upload an Excel file for processing.
    """
    try:
        # Validate file
        validation_result = await validate_file(file)
        if not validation_result.is_valid:
            raise HTTPException(
                status_code=400,
                detail=validation_result.error_message
            )
        
        # Generate unique file ID
        file_id = str(uuid.uuid4())
        
        # Save uploaded file
        file_path = await file_service.save_uploaded_file(file, file_id)
        
        # Calculate file hash
        file_hash = await calculate_file_hash(file_path)
        
        # Create metadata
        metadata = ExcelFileMetadata(
            file_id=file_id,
            filename=file.filename,
            file_size=file.size,
            file_hash=file_hash,
            mime_type=file.content_type,
            status=FileStatus.PROCESSING
        )
        
        # Save metadata
        await file_service.save_metadata(file_id, metadata)
        
        # Start background processing
        background_tasks.add_task(
            excel_service.process_excel_file,
            file_id,
            file_path
        )
        
        logger.info("File uploaded successfully", file_id=file_id, filename=file.filename)
        
        return ProcessingStatus(
            file_id=file_id,
            status=FileStatus.PROCESSING,
            progress=0.0,
            message="File uploaded, processing started"
        )
        
    except Exception as e:
        logger.error("File upload failed", error=str(e))
        raise HTTPException(status_code=500, detail="File upload failed")


@router.get("/status/{file_id}", response_model=ProcessingStatus)
async def get_file_status(file_id: str):
    """
    Get file processing status.
    """
    try:
        metadata = await file_service.get_metadata(file_id)
        if not metadata:
            raise HTTPException(status_code=404, detail="File not found")
        
        # Calculate progress based on status
        progress_map = {
            FileStatus.UPLOADING: 10.0,
            FileStatus.PROCESSING: 50.0,
            FileStatus.READY: 100.0,
            FileStatus.ERROR: 0.0
        }
        
        return ProcessingStatus(
            file_id=file_id,
            status=metadata.status,
            progress=progress_map.get(metadata.status, 0.0),
            message=f"File is {metadata.status.value}",
            error=metadata.error_message
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get file status", file_id=file_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get file status")


@router.get("/list", response_model=List[ExcelFileMetadata])
async def list_files():
    """
    List all uploaded files.
    """
    try:
        files = await file_service.list_files()
        return files
    except Exception as e:
        logger.error("Failed to list files", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to list files")


@router.delete("/{file_id}")
async def delete_file(file_id: str):
    """
    Delete a file and its associated data.
    """
    try:
        success = await file_service.delete_file(file_id)
        if not success:
            raise HTTPException(status_code=404, detail="File not found")
        
        logger.info("File deleted successfully", file_id=file_id)
        return {"message": "File deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to delete file", file_id=file_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to delete file")


@router.get("/{file_id}/metadata", response_model=ExcelFileMetadata)
async def get_file_metadata(file_id: str):
    """
    Get detailed file metadata.
    """
    try:
        metadata = await file_service.get_metadata(file_id)
        if not metadata:
            raise HTTPException(status_code=404, detail="File not found")
        
        return metadata
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get file metadata", file_id=file_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get file metadata")
